import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '@shared/types';

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      if (typeof window !== 'undefined') {
        const tokens = localStorage.getItem('auth_tokens');
        if (tokens) {
          try {
            const parsedTokens = JSON.parse(tokens);
            config.headers.Authorization = `Bearer ${parsedTokens.accessToken}`;
          } catch (error) {
            console.error('Failed to parse auth tokens:', error);
          }
        }
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle errors
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Token expired or invalid
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_tokens');
          window.location.href = '/auth/login';
        }
      }
      return Promise.reject(error);
    }
  );

  return client;
};

const apiClient = createApiClient();

// Helper function to handle API responses
const handleApiResponse = <T>(response: AxiosResponse<ApiResponse<T>>): T => {
  if (!response.data.success) {
    throw new Error(response.data.error || 'API request failed');
  }
  return response.data.data as T;
};

// Auth API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await apiClient.post('/api/auth/login', { email, password });
    return handleApiResponse(response);
  },

  register: async (data: {
    email: string;
    username: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) => {
    const response = await apiClient.post('/api/auth/register', data);
    return handleApiResponse(response);
  },

  logout: async (refreshToken: string) => {
    const response = await apiClient.post('/api/auth/logout', { refreshToken });
    return handleApiResponse(response);
  },

  refreshToken: async (refreshToken: string) => {
    const response = await apiClient.post('/api/auth/refresh', { refreshToken });
    return handleApiResponse(response);
  },

  getProfile: async () => {
    const response = await apiClient.get('/api/auth/profile');
    return handleApiResponse(response);
  },
};

// Ollama API
export const ollamaApi = {
  listModels: async () => {
    const response = await apiClient.get('/api/ollama/models');
    return handleApiResponse(response);
  },

  getModelInfo: async (name: string) => {
    const response = await apiClient.get(`/api/ollama/models/${name}`);
    return handleApiResponse(response);
  },

  pullModel: async (data: { name: string; stream?: boolean }) => {
    const response = await apiClient.post('/api/ollama/pull', data);
    return handleApiResponse(response);
  },

  deleteModel: async (name: string) => {
    const response = await apiClient.delete(`/api/ollama/models/${name}`);
    return handleApiResponse(response);
  },

  sendChatMessage: async (data: {
    message: string;
    model: string;
    conversationId?: string;
    stream?: boolean;
  }) => {
    const response = await apiClient.post('/api/ollama/chat', data);
    return handleApiResponse(response);
  },

  getConversations: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    const response = await apiClient.get('/api/ollama/conversations', { params });
    return handleApiResponse(response);
  },

  getConversation: async (id: string) => {
    const response = await apiClient.get(`/api/ollama/conversations/${id}`);
    return handleApiResponse(response);
  },

  updateConversation: async (id: string, data: { title: string }) => {
    const response = await apiClient.put(`/api/ollama/conversations/${id}`, data);
    return handleApiResponse(response);
  },

  deleteConversation: async (id: string) => {
    const response = await apiClient.delete(`/api/ollama/conversations/${id}`);
    return handleApiResponse(response);
  },
};

// ComfyUI API
export const comfyuiApi = {
  getWorkflows: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    const response = await apiClient.get('/api/comfyui/workflows', { params });
    return handleApiResponse(response);
  },

  createWorkflow: async (data: any) => {
    const response = await apiClient.post('/api/comfyui/workflows', data);
    return handleApiResponse(response);
  },

  getWorkflow: async (id: string) => {
    const response = await apiClient.get(`/api/comfyui/workflows/${id}`);
    return handleApiResponse(response);
  },

  updateWorkflow: async (id: string, data: any) => {
    const response = await apiClient.put(`/api/comfyui/workflows/${id}`, data);
    return handleApiResponse(response);
  },

  deleteWorkflow: async (id: string) => {
    const response = await apiClient.delete(`/api/comfyui/workflows/${id}`);
    return handleApiResponse(response);
  },

  executeWorkflow: async (data: { workflowId: string; inputs?: any }) => {
    const response = await apiClient.post('/api/comfyui/execute', data);
    return handleApiResponse(response);
  },

  getQueue: async () => {
    const response = await apiClient.get('/api/comfyui/queue');
    return handleApiResponse(response);
  },

  getQueueItem: async (id: string) => {
    const response = await apiClient.get(`/api/comfyui/queue/${id}`);
    return handleApiResponse(response);
  },

  cancelQueueItem: async (id: string) => {
    const response = await apiClient.delete(`/api/comfyui/queue/${id}`);
    return handleApiResponse(response);
  },

  getNodes: async () => {
    const response = await apiClient.get('/api/comfyui/nodes');
    return handleApiResponse(response);
  },

  getHistory: async (params?: {
    page?: number;
    limit?: number;
  }) => {
    const response = await apiClient.get('/api/comfyui/history', { params });
    return handleApiResponse(response);
  },
};

// Augment Code API
export const augmentApi = {
  getProjects: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) => {
    const response = await apiClient.get('/api/augment/projects', { params });
    return handleApiResponse(response);
  },

  createProject: async (data: {
    name: string;
    path: string;
    language: string;
    framework?: string;
  }) => {
    const response = await apiClient.post('/api/augment/projects', data);
    return handleApiResponse(response);
  },

  getProject: async (id: string) => {
    const response = await apiClient.get(`/api/augment/projects/${id}`);
    return handleApiResponse(response);
  },

  updateProject: async (id: string, data: any) => {
    const response = await apiClient.put(`/api/augment/projects/${id}`, data);
    return handleApiResponse(response);
  },

  deleteProject: async (id: string) => {
    const response = await apiClient.delete(`/api/augment/projects/${id}`);
    return handleApiResponse(response);
  },

  getProjectFiles: async (id: string) => {
    const response = await apiClient.get(`/api/augment/projects/${id}/files`);
    return handleApiResponse(response);
  },

  createFile: async (projectId: string, data: any) => {
    const response = await apiClient.post(`/api/augment/projects/${projectId}/files`, data);
    return handleApiResponse(response);
  },

  getFile: async (id: string) => {
    const response = await apiClient.get(`/api/augment/files/${id}`);
    return handleApiResponse(response);
  },

  updateFile: async (id: string, data: any) => {
    const response = await apiClient.put(`/api/augment/files/${id}`, data);
    return handleApiResponse(response);
  },

  deleteFile: async (id: string) => {
    const response = await apiClient.delete(`/api/augment/files/${id}`);
    return handleApiResponse(response);
  },

  getSuggestions: async (data: {
    code: string;
    language: string;
    cursorPosition: { line: number; column: number };
    context?: string;
  }) => {
    const response = await apiClient.post('/api/augment/suggestions', data);
    return handleApiResponse(response);
  },

  analyzeCode: async (data: {
    code: string;
    language: string;
    filePath?: string;
  }) => {
    const response = await apiClient.post('/api/augment/analyze', data);
    return handleApiResponse(response);
  },
};

// System API
export const systemApi = {
  getStatus: async () => {
    const response = await apiClient.get('/api/system/status');
    return handleApiResponse(response);
  },

  getMetrics: async () => {
    const response = await apiClient.get('/api/system/metrics');
    return handleApiResponse(response);
  },

  getHealth: async () => {
    const response = await apiClient.get('/api/system/health');
    return handleApiResponse(response);
  },
};

export default apiClient;
