#!/bin/bash

# Health check script for AI Development Stack services
# This script checks the health of all services and reports status

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:3000"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
REDIS_HOST="localhost"
REDIS_PORT="6379"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

check_http_service() {
    local name=$1
    local url=$2
    local timeout=${3:-10}
    
    if curl -f -s --max-time $timeout "$url" > /dev/null 2>&1; then
        log_success "$name is healthy"
        return 0
    else
        log_error "$name is not responding"
        return 1
    fi
}

check_tcp_service() {
    local name=$1
    local host=$2
    local port=$3
    local timeout=${4:-5}
    
    if timeout $timeout bash -c "cat < /dev/null > /dev/tcp/$host/$port" 2>/dev/null; then
        log_success "$name is reachable"
        return 0
    else
        log_error "$name is not reachable"
        return 1
    fi
}

check_docker_service() {
    local service_name=$1
    
    if docker-compose ps | grep -q "$service_name.*Up"; then
        log_success "Docker service '$service_name' is running"
        return 0
    else
        log_error "Docker service '$service_name' is not running"
        return 1
    fi
}

check_backend_health() {
    log_info "Checking backend health..."
    
    # Check if backend container is running
    if ! check_docker_service "backend"; then
        return 1
    fi
    
    # Check backend HTTP endpoint
    if ! check_http_service "Backend API" "$BACKEND_URL/health"; then
        return 1
    fi
    
    # Check backend API endpoints
    if curl -f -s "$BACKEND_URL/api/system/status" > /dev/null 2>&1; then
        log_success "Backend API endpoints are responding"
    else
        log_warning "Backend API endpoints may not be fully ready"
    fi
    
    return 0
}

check_frontend_health() {
    log_info "Checking frontend health..."
    
    # Check if frontend container is running
    if ! check_docker_service "frontend"; then
        return 1
    fi
    
    # Check frontend HTTP endpoint
    if ! check_http_service "Frontend" "$FRONTEND_URL"; then
        return 1
    fi
    
    return 0
}

check_database_health() {
    log_info "Checking database health..."
    
    # Check if postgres container is running
    if ! check_docker_service "postgres"; then
        return 1
    fi
    
    # Check PostgreSQL TCP connection
    if ! check_tcp_service "PostgreSQL" "$POSTGRES_HOST" "$POSTGRES_PORT"; then
        return 1
    fi
    
    # Check database connection from backend
    if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
        log_success "PostgreSQL is ready to accept connections"
    else
        log_error "PostgreSQL is not ready"
        return 1
    fi
    
    return 0
}

check_redis_health() {
    log_info "Checking Redis health..."
    
    # Check if redis container is running
    if ! check_docker_service "redis"; then
        return 1
    fi
    
    # Check Redis TCP connection
    if ! check_tcp_service "Redis" "$REDIS_HOST" "$REDIS_PORT"; then
        return 1
    fi
    
    # Check Redis ping
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis is responding to ping"
    else
        log_error "Redis is not responding to ping"
        return 1
    fi
    
    return 0
}

check_external_services() {
    log_info "Checking external services..."
    
    # Check Ollama (optional)
    if curl -f -s --max-time 5 "http://localhost:11434/api/tags" > /dev/null 2>&1; then
        log_success "Ollama service is available"
    else
        log_warning "Ollama service is not available (this is optional)"
    fi
    
    # Check ComfyUI (optional)
    if curl -f -s --max-time 5 "http://localhost:8188/queue" > /dev/null 2>&1; then
        log_success "ComfyUI service is available"
    else
        log_warning "ComfyUI service is not available (this is optional)"
    fi
}

check_system_resources() {
    log_info "Checking system resources..."
    
    # Check disk space
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 90 ]; then
        log_success "Disk usage is acceptable ($DISK_USAGE%)"
    else
        log_warning "Disk usage is high ($DISK_USAGE%)"
    fi
    
    # Check memory usage
    if command -v free > /dev/null 2>&1; then
        MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [ "$MEMORY_USAGE" -lt 90 ]; then
            log_success "Memory usage is acceptable ($MEMORY_USAGE%)"
        else
            log_warning "Memory usage is high ($MEMORY_USAGE%)"
        fi
    fi
    
    # Check Docker daemon
    if docker info > /dev/null 2>&1; then
        log_success "Docker daemon is running"
    else
        log_error "Docker daemon is not running"
        return 1
    fi
}

generate_report() {
    local total_checks=$1
    local failed_checks=$2
    local warnings=$3
    
    echo
    echo "=================================="
    echo "Health Check Report"
    echo "=================================="
    echo "Total checks: $total_checks"
    echo "Failed checks: $failed_checks"
    echo "Warnings: $warnings"
    echo
    
    if [ $failed_checks -eq 0 ]; then
        if [ $warnings -eq 0 ]; then
            log_success "All systems are healthy!"
        else
            log_warning "Systems are mostly healthy with $warnings warning(s)"
        fi
        echo "✅ Ready to use: http://localhost:3000"
    else
        log_error "Some systems are not healthy. Please check the logs above."
        echo "❌ Application may not function properly"
    fi
    
    echo
    echo "Useful commands:"
    echo "  View logs: docker-compose logs -f"
    echo "  Restart services: docker-compose restart"
    echo "  Check status: docker-compose ps"
}

# Main health check
main() {
    echo "🏥 AI Development Stack Health Check"
    echo "===================================="
    echo
    
    local total_checks=0
    local failed_checks=0
    local warnings=0
    
    # Core services
    total_checks=$((total_checks + 1))
    if ! check_database_health; then
        failed_checks=$((failed_checks + 1))
    fi
    
    total_checks=$((total_checks + 1))
    if ! check_redis_health; then
        failed_checks=$((failed_checks + 1))
    fi
    
    total_checks=$((total_checks + 1))
    if ! check_backend_health; then
        failed_checks=$((failed_checks + 1))
    fi
    
    total_checks=$((total_checks + 1))
    if ! check_frontend_health; then
        failed_checks=$((failed_checks + 1))
    fi
    
    # System resources
    total_checks=$((total_checks + 1))
    if ! check_system_resources; then
        failed_checks=$((failed_checks + 1))
    fi
    
    # External services (warnings only)
    check_external_services
    
    # Generate report
    generate_report $total_checks $failed_checks $warnings
    
    # Exit with appropriate code
    if [ $failed_checks -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Handle script arguments
case "${1:-check}" in
    "check")
        main
        ;;
    "backend")
        check_backend_health
        ;;
    "frontend")
        check_frontend_health
        ;;
    "database")
        check_database_health
        ;;
    "redis")
        check_redis_health
        ;;
    "external")
        check_external_services
        ;;
    "system")
        check_system_resources
        ;;
    "help")
        echo "Health Check Script for AI Development Stack"
        echo
        echo "Usage: $0 [component]"
        echo
        echo "Components:"
        echo "  check      - Check all components (default)"
        echo "  backend    - Check backend service only"
        echo "  frontend   - Check frontend service only"
        echo "  database   - Check database service only"
        echo "  redis      - Check Redis service only"
        echo "  external   - Check external services only"
        echo "  system     - Check system resources only"
        echo "  help       - Show this help"
        ;;
    *)
        log_error "Unknown component: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
