// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    REFRESH: '/api/auth/refresh',
    LOGOUT: '/api/auth/logout',
    PROFILE: '/api/auth/profile',
  },
  
  // Ollama
  OLLAMA: {
    MODELS: '/api/ollama/models',
    MODEL_INFO: '/api/ollama/models/:name',
    PULL_MODEL: '/api/ollama/pull',
    DELETE_MODEL: '/api/ollama/delete',
    CHAT: '/api/ollama/chat',
    CONVERSATIONS: '/api/ollama/conversations',
    CONVERSATION: '/api/ollama/conversations/:id',
  },
  
  // ComfyUI
  COMFYUI: {
    WORKFLOWS: '/api/comfyui/workflows',
    WORKFLOW: '/api/comfyui/workflows/:id',
    QUEUE: '/api/comfyui/queue',
    QUEUE_ITEM: '/api/comfyui/queue/:id',
    EXECUTE: '/api/comfyui/execute',
    NODES: '/api/comfyui/nodes',
    HISTORY: '/api/comfyui/history',
  },
  
  // Augment Code
  AUGMENT: {
    PROJECTS: '/api/augment/projects',
    PROJECT: '/api/augment/projects/:id',
    FILES: '/api/augment/projects/:id/files',
    FILE: '/api/augment/files/:id',
    SUGGESTIONS: '/api/augment/suggestions',
    ANALYZE: '/api/augment/analyze',
  },
  
  // System
  SYSTEM: {
    STATUS: '/api/system/status',
    METRICS: '/api/system/metrics',
    HEALTH: '/api/system/health',
  },
} as const;

// WebSocket Events
export const WS_EVENTS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  
  // Authentication
  AUTHENTICATE: 'authenticate',
  AUTHENTICATED: 'authenticated',
  
  // Chat
  CHAT_MESSAGE: 'chat_message',
  CHAT_STREAM: 'chat_stream',
  CHAT_ERROR: 'chat_error',
  
  // ComfyUI
  QUEUE_UPDATE: 'queue_update',
  WORKFLOW_PROGRESS: 'workflow_progress',
  GENERATION_COMPLETE: 'generation_complete',
  
  // System
  SYSTEM_STATUS: 'system_status',
  METRICS_UPDATE: 'metrics_update',
  
  // General
  NOTIFICATION: 'notification',
} as const;

// Application Constants
export const APP_CONFIG = {
  NAME: 'AI Development Stack GUI',
  VERSION: '1.0.0',
  DESCRIPTION: 'Unified frontend for local AI development tools',
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // File Upload
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_CODE_EXTENSIONS: ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.go', '.rs', '.php'],
  
  // Chat
  MAX_MESSAGE_LENGTH: 4000,
  MAX_CONVERSATION_HISTORY: 100,
  
  // Workflow
  MAX_WORKFLOW_NODES: 1000,
  MAX_WORKFLOW_SIZE: 10 * 1024 * 1024, // 10MB
  
  // Timeouts
  API_TIMEOUT: 30000, // 30 seconds
  WS_RECONNECT_INTERVAL: 5000, // 5 seconds
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
} as const;

// Error Codes
export const ERROR_CODES = {
  // Authentication
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // External Services
  OLLAMA_UNAVAILABLE: 'OLLAMA_UNAVAILABLE',
  COMFYUI_UNAVAILABLE: 'COMFYUI_UNAVAILABLE',
  AUGMENT_CODE_UNAVAILABLE: 'AUGMENT_CODE_UNAVAILABLE',
  
  // System
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // File Operations
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  
  // Database
  DATABASE_ERROR: 'DATABASE_ERROR',
  RECORD_NOT_FOUND: 'RECORD_NOT_FOUND',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// UI Constants
export const UI_CONFIG = {
  // Theme
  THEME: {
    LIGHT: 'light',
    DARK: 'dark',
    SYSTEM: 'system',
  },
  
  // Layout
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  FOOTER_HEIGHT: 48,
  
  // Breakpoints (Tailwind CSS)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },
  
  // Animation Durations
  ANIMATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  // Z-Index Layers
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
} as const;
