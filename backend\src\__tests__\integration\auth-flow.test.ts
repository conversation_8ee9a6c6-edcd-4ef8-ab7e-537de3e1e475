import request from 'supertest';
import { app } from '../../app';
import { getPrismaClient } from '../../utils/database';

const prisma = getPrismaClient();

describe('Authentication Flow Integration', () => {
  beforeEach(async () => {
    // Clean up database before each test
    await prisma.refreshToken.deleteMany();
    await prisma.user.deleteMany();
  });

  afterAll(async () => {
    // Clean up and disconnect
    await prisma.refreshToken.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('Complete Authentication Flow', () => {
    it('should complete full registration -> login -> access protected route -> logout flow', async () => {
      // Step 1: Register a new user
      const userData = {
        email: '<EMAIL>',
        username: 'integrationuser',
        password: 'Integration123!@#',
        confirmPassword: 'Integration123!@#',
        firstName: 'Integration',
        lastName: 'User',
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.email).toBe(userData.email);
      
      const { accessToken, refreshToken } = registerResponse.body.data.tokens;
      expect(accessToken).toBeDefined();
      expect(refreshToken).toBeDefined();

      // Step 2: Access protected route with access token
      const protectedResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(protectedResponse.body.success).toBe(true);
      expect(protectedResponse.body.data.user.email).toBe(userData.email);

      // Step 3: Logout
      const logoutResponse = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ refreshToken })
        .expect(200);

      expect(logoutResponse.body.success).toBe(true);

      // Step 4: Verify refresh token is invalidated
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(401);

      expect(refreshResponse.body.success).toBe(false);

      // Step 5: Verify access token is invalidated (if using token blacklist)
      const protectedAfterLogoutResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(401);

      expect(protectedAfterLogoutResponse.body.success).toBe(false);
    });

    it('should handle token refresh flow correctly', async () => {
      // Register and login
      const userData = {
        email: '<EMAIL>',
        username: 'refreshuser',
        password: 'Refresh123!@#',
        confirmPassword: 'Refresh123!@#',
        firstName: 'Refresh',
        lastName: 'User',
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      const { refreshToken: originalRefreshToken } = loginResponse.body.data.tokens;

      // Wait a moment to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Refresh tokens
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: originalRefreshToken })
        .expect(200);

      expect(refreshResponse.body.success).toBe(true);
      
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } = refreshResponse.body.data.tokens;
      expect(newAccessToken).toBeDefined();
      expect(newRefreshToken).toBeDefined();
      expect(newRefreshToken).not.toBe(originalRefreshToken);

      // Verify new access token works
      const protectedResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${newAccessToken}`)
        .expect(200);

      expect(protectedResponse.body.success).toBe(true);

      // Verify old refresh token is invalidated
      const oldRefreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: originalRefreshToken })
        .expect(401);

      expect(oldRefreshResponse.body.success).toBe(false);
    });

    it('should handle concurrent login sessions correctly', async () => {
      // Register user
      const userData = {
        email: '<EMAIL>',
        username: 'concurrentuser',
        password: 'Concurrent123!@#',
        confirmPassword: 'Concurrent123!@#',
        firstName: 'Concurrent',
        lastName: 'User',
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Login from multiple sessions
      const session1Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      const session2Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      const session1Tokens = session1Response.body.data.tokens;
      const session2Tokens = session2Response.body.data.tokens;

      // Both sessions should have different tokens
      expect(session1Tokens.accessToken).not.toBe(session2Tokens.accessToken);
      expect(session1Tokens.refreshToken).not.toBe(session2Tokens.refreshToken);

      // Both sessions should work independently
      const session1Protected = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .expect(200);

      const session2Protected = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(200);

      expect(session1Protected.body.success).toBe(true);
      expect(session2Protected.body.success).toBe(true);

      // Logout from session 1 should not affect session 2
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .send({ refreshToken: session1Tokens.refreshToken })
        .expect(200);

      // Session 1 should be invalidated
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .expect(401);

      // Session 2 should still work
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(200);
    });
  });

  describe('Error Scenarios', () => {
    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test a scenario that might cause database issues
      
      const userData = {
        email: '<EMAIL>',
        username: 'erroruser',
        password: 'Error123!@#',
        confirmPassword: 'Error123!@#',
        firstName: 'Error',
        lastName: 'User',
      };

      // Register user
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Try to register same user again (should cause database constraint error)
      const duplicateResponse = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(duplicateResponse.body.success).toBe(false);
      expect(duplicateResponse.body.error).toContain('already exists');
    });

    it('should handle malformed JWT tokens', async () => {
      // Test with completely invalid token
      const invalidTokenResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(invalidTokenResponse.body.success).toBe(false);

      // Test with malformed Bearer header
      const malformedHeaderResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'InvalidFormat token')
        .expect(401);

      expect(malformedHeaderResponse.body.success).toBe(false);

      // Test with empty Authorization header
      const emptyHeaderResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', '')
        .expect(401);

      expect(emptyHeaderResponse.body.success).toBe(false);
    });

    it('should handle expired tokens correctly', async () => {
      // This test would require creating tokens with very short expiration
      // For integration testing, we can test the refresh flow instead
      
      const userData = {
        email: '<EMAIL>',
        username: 'expireduser',
        password: 'Expired123!@#',
        confirmPassword: 'Expired123!@#',
        firstName: 'Expired',
        lastName: 'User',
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      const { refreshToken } = registerResponse.body.data.tokens;

      // Test refresh with valid token
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(refreshResponse.body.success).toBe(true);
    });
  });

  describe('Rate Limiting and Security', () => {
    it('should handle multiple failed login attempts', async () => {
      // Register user first
      const userData = {
        email: '<EMAIL>',
        username: 'ratelimituser',
        password: 'RateLimit123!@#',
        confirmPassword: 'RateLimit123!@#',
        firstName: 'RateLimit',
        lastName: 'User',
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Attempt multiple failed logins
      const failedAttempts = [];
      for (let i = 0; i < 5; i++) {
        failedAttempts.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: userData.email,
              password: 'WrongPassword123!@#',
            })
        );
      }

      const responses = await Promise.all(failedAttempts);
      
      // All should fail with 401
      responses.forEach(response => {
        expect(response.status).toBe(401);
        expect(response.body.success).toBe(false);
      });

      // Correct login should still work (unless rate limiting is implemented)
      const correctLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      expect(correctLoginResponse.body.success).toBe(true);
    });

    it('should validate input sanitization', async () => {
      // Test with potentially malicious input
      const maliciousData = {
        email: '<script>alert("xss")</script>@example.com',
        username: 'user<script>',
        password: 'Password123!@#',
        confirmPassword: 'Password123!@#',
        firstName: '<img src=x onerror=alert(1)>',
        lastName: 'User',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(maliciousData)
        .expect(400); // Should fail validation

      expect(response.body.success).toBe(false);
    });
  });
});
