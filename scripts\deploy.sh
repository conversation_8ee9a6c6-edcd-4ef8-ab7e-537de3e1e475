#!/bin/bash

# AI Development Stack GUI Deployment Script
# This script handles deployment to production environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"
PROD_COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.${ENVIRONMENT}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found. Please create it first."
        exit 1
    fi
    
    log_success "All requirements met"
}

backup_data() {
    log_info "Creating backup of existing data..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker-compose ps | grep -q postgres; then
        log_info "Backing up database..."
        docker-compose exec -T postgres pg_dump -U postgres ai_dev_stack > "$BACKUP_DIR/database.sql"
        log_success "Database backup created: $BACKUP_DIR/database.sql"
    fi
    
    # Backup uploads
    if [ -d "backend/uploads" ]; then
        log_info "Backing up uploads..."
        cp -r backend/uploads "$BACKUP_DIR/"
        log_success "Uploads backup created: $BACKUP_DIR/uploads"
    fi
    
    log_success "Backup completed: $BACKUP_DIR"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build -t ai-dev-stack-backend:latest ./backend
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -t ai-dev-stack-frontend:latest ./frontend
    
    log_success "Images built successfully"
}

deploy_services() {
    log_info "Deploying services..."
    
    # Load environment variables
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "Deploying to production environment..."
        docker-compose -f "$COMPOSE_FILE" -f "$PROD_COMPOSE_FILE" up -d
    else
        log_info "Deploying to $ENVIRONMENT environment..."
        docker-compose -f "$COMPOSE_FILE" up -d
    fi
    
    log_success "Services deployed"
}

run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run Prisma migrations
    docker-compose exec backend npx prisma migrate deploy
    
    log_success "Migrations completed"
}

health_check() {
    log_info "Performing health checks..."
    
    # Check backend health
    log_info "Checking backend health..."
    for i in {1..30}; do
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            log_success "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Backend health check failed"
            exit 1
        fi
        sleep 2
    done
    
    # Check frontend health
    log_info "Checking frontend health..."
    for i in {1..30}; do
        if curl -f http://localhost:3000 > /dev/null 2>&1; then
            log_success "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Frontend health check failed"
            exit 1
        fi
        sleep 2
    done
    
    log_success "All health checks passed"
}

setup_ssl() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "Setting up SSL certificates..."
        
        # Create SSL directory if it doesn't exist
        mkdir -p nginx/ssl
        
        # Generate self-signed certificate if none exists
        if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
            log_warning "SSL certificates not found. Generating self-signed certificates..."
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout nginx/ssl/key.pem \
                -out nginx/ssl/cert.pem \
                -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
            log_success "Self-signed SSL certificates generated"
        else
            log_success "SSL certificates found"
        fi
    fi
}

cleanup_old_images() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old images (keep last 3 versions)
    docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
        grep "ai-dev-stack" | \
        tail -n +4 | \
        awk '{print $1}' | \
        xargs -r docker rmi
    
    log_success "Cleanup completed"
}

show_status() {
    log_info "Deployment status:"
    echo
    docker-compose ps
    echo
    log_info "Application URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:3001"
    if [ "$ENVIRONMENT" = "production" ]; then
        echo "  HTTPS: https://localhost"
    fi
    echo
}

rollback() {
    log_warning "Rolling back deployment..."
    
    # Stop current services
    docker-compose down
    
    # Restore from latest backup
    LATEST_BACKUP=$(ls -t backups/ | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        log_info "Restoring from backup: $LATEST_BACKUP"
        
        # Restore database
        if [ -f "backups/$LATEST_BACKUP/database.sql" ]; then
            docker-compose up -d postgres
            sleep 10
            docker-compose exec -T postgres psql -U postgres -d ai_dev_stack < "backups/$LATEST_BACKUP/database.sql"
        fi
        
        # Restore uploads
        if [ -d "backups/$LATEST_BACKUP/uploads" ]; then
            rm -rf backend/uploads
            cp -r "backups/$LATEST_BACKUP/uploads" backend/
        fi
        
        log_success "Rollback completed"
    else
        log_error "No backup found for rollback"
        exit 1
    fi
}

# Main deployment flow
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    
    case "${2:-deploy}" in
        "rollback")
            rollback
            ;;
        "deploy")
            check_requirements
            backup_data
            setup_ssl
            build_images
            deploy_services
            run_migrations
            health_check
            cleanup_old_images
            show_status
            log_success "Deployment completed successfully!"
            ;;
        "status")
            show_status
            ;;
        *)
            echo "Usage: $0 [environment] [deploy|rollback|status]"
            echo "  environment: production, staging, development (default: production)"
            echo "  action: deploy, rollback, status (default: deploy)"
            exit 1
            ;;
    esac
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
