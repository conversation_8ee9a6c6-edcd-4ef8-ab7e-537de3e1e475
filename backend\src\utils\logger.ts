import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { config } from '@/config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// Define colors for console output
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
};

winston.addColors(colors);

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports
const transports: winston.transport[] = [];

// Console transport (always enabled in development)
if (config.isDevelopment || config.isTest) {
  transports.push(
    new winston.transports.Console({
      level: config.logging.level,
      format: consoleFormat,
    })
  );
}

// File transport (for production and development)
if (!config.isTest) {
  // Daily rotate file for all logs
  transports.push(
    new DailyRotateFile({
      filename: config.logging.file.replace('.log', '-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      level: config.logging.level,
      format: fileFormat,
    })
  );

  // Separate file for errors only
  transports.push(
    new DailyRotateFile({
      filename: config.logging.file.replace('.log', '-error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error',
      format: fileFormat,
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  levels,
  level: config.logging.level,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Create a stream for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};

// Helper functions for structured logging
export const logError = (message: string, error?: Error | unknown, meta?: object) => {
  const errorMeta = error instanceof Error 
    ? { 
        name: error.name, 
        message: error.message, 
        stack: error.stack,
        ...meta 
      }
    : { error, ...meta };
    
  logger.error(message, errorMeta);
};

export const logInfo = (message: string, meta?: object) => {
  logger.info(message, meta);
};

export const logWarn = (message: string, meta?: object) => {
  logger.warn(message, meta);
};

export const logDebug = (message: string, meta?: object) => {
  logger.debug(message, meta);
};

// Performance logging helper
export const logPerformance = (operation: string, startTime: number, meta?: object) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation}`, { 
    duration: `${duration}ms`,
    ...meta 
  });
};

// Request logging helper
export const logRequest = (method: string, url: string, statusCode: number, duration: number, meta?: object) => {
  const level = statusCode >= 400 ? 'warn' : 'info';
  logger.log(level, `${method} ${url} ${statusCode}`, {
    method,
    url,
    statusCode,
    duration: `${duration}ms`,
    ...meta,
  });
};

// Database operation logging
export const logDatabase = (operation: string, table: string, duration?: number, meta?: object) => {
  logger.debug(`Database: ${operation} on ${table}`, {
    operation,
    table,
    duration: duration ? `${duration}ms` : undefined,
    ...meta,
  });
};

// External service logging
export const logExternalService = (service: string, operation: string, success: boolean, duration?: number, meta?: object) => {
  const level = success ? 'info' : 'warn';
  logger.log(level, `External Service: ${service} ${operation}`, {
    service,
    operation,
    success,
    duration: duration ? `${duration}ms` : undefined,
    ...meta,
  });
};

// WebSocket logging
export const logWebSocket = (event: string, socketId: string, meta?: object) => {
  logger.debug(`WebSocket: ${event}`, {
    event,
    socketId,
    ...meta,
  });
};

// Security logging
export const logSecurity = (event: string, userId?: string, ip?: string, meta?: object) => {
  logger.warn(`Security: ${event}`, {
    event,
    userId,
    ip,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

export default logger;
