import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { getPrismaClient } from '@/utils/database';
import { logSecurity } from '@/utils/logger';
import { AuthenticationError, AuthorizationError } from '@/middleware/errorHandler';
import { User } from '@shared/types';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

// Extract token from request
function extractToken(req: Request): string | null {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (for WebSocket connections)
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }
  
  return null;
}

// Verify JWT token
function verifyToken(token: string): JWTPayload {
  try {
    return jwt.verify(token, config.jwt.secret) as JWTPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AuthenticationError('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid token');
    } else {
      throw new AuthenticationError('Token verification failed');
    }
  }
}

// Authentication middleware
export async function authenticate(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      throw new AuthenticationError('No token provided');
    }

    const payload = verifyToken(token);
    const prisma = getPrismaClient();

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      logSecurity('Authentication failed: User not found', payload.userId, req.ip);
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      logSecurity('Authentication failed: User inactive', payload.userId, req.ip);
      throw new AuthenticationError('Account is inactive');
    }

    // Add user to request
    req.user = user;
    next();
    
  } catch (error) {
    if (error instanceof AuthenticationError) {
      next(error);
    } else {
      logSecurity('Authentication error', undefined, req.ip, { error: error.message });
      next(new AuthenticationError('Authentication failed'));
    }
  }
}

// Optional authentication middleware (doesn't throw if no token)
export async function optionalAuthenticate(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return next();
    }

    const payload = verifyToken(token);
    const prisma = getPrismaClient();

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (user && user.isActive) {
      req.user = user;
    }

    next();
    
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
}

// Authorization middleware factory
export function authorize(...roles: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    if (roles.length > 0 && !roles.includes(req.user.role)) {
      logSecurity('Authorization failed: Insufficient permissions', req.user.id, req.ip, {
        requiredRoles: roles,
        userRole: req.user.role,
      });
      throw new AuthorizationError('Insufficient permissions');
    }

    next();
  };
}

// Check if user owns resource
export function requireOwnership(getResourceUserId: (req: Request) => Promise<string>) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const resourceUserId = await getResourceUserId(req);
      
      if (req.user.id !== resourceUserId && req.user.role !== 'ADMIN') {
        logSecurity('Authorization failed: Resource ownership', req.user.id, req.ip, {
          resourceUserId,
          action: 'ownership_check',
        });
        throw new AuthorizationError('You can only access your own resources');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

// Generate JWT token
export function generateToken(user: { id: string; email: string; role: string }): string {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
    },
    config.jwt.secret,
    {
      expiresIn: config.jwt.expiresIn,
    }
  );
}

// Generate refresh token
export function generateRefreshToken(user: { id: string; email: string; role: string }): string {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
      type: 'refresh',
    },
    config.jwt.secret,
    {
      expiresIn: config.jwt.refreshExpiresIn,
    }
  );
}

// Verify refresh token
export function verifyRefreshToken(token: string): JWTPayload & { type: string } {
  try {
    const payload = jwt.verify(token, config.jwt.secret) as JWTPayload & { type: string };
    
    if (payload.type !== 'refresh') {
      throw new AuthenticationError('Invalid refresh token');
    }
    
    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AuthenticationError('Refresh token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid refresh token');
    } else {
      throw new AuthenticationError('Refresh token verification failed');
    }
  }
}

// WebSocket authentication helper
export async function authenticateSocket(token: string): Promise<User | null> {
  try {
    if (!token) {
      return null;
    }

    const payload = verifyToken(token);
    const prisma = getPrismaClient();

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user || !user.isActive) {
      return null;
    }

    return user;
  } catch (error) {
    return null;
  }
}
