# Setup Guide

This guide will help you set up the AI Development Stack GUI on your local machine.

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space minimum
- **CPU**: 4+ cores recommended for optimal performance

### Required Software
- **Docker Desktop** 4.0+ ([Download](https://www.docker.com/products/docker-desktop))
- **Docker Compose** 2.0+ (included with Docker Desktop)
- **Git** ([Download](https://git-scm.com/downloads))

### Optional Software (for local development)
- **Node.js** 18+ ([Download](https://nodejs.org/))
- **npm** 8+ (included with Node.js)

## Quick Start (Docker)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ai-development-stack-gui
```

### 2. Run Setup Script
```bash
# Make script executable (Linux/macOS)
chmod +x scripts/setup.sh

# Run setup
./scripts/setup.sh
```

The setup script will:
- Check system requirements
- Create environment files
- Install dependencies
- Set up the database
- Build Docker images
- Start all services

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Database**: localhost:5432
- **Redis**: localhost:6379

### 4. Login
Use one of the sample accounts:
- **Admin**: <EMAIL> / admin123!@#
- **Demo**: <EMAIL> / demo123!@#

## Manual Setup

### 1. Environment Configuration

#### Backend Environment
Copy and configure the backend environment:
```bash
cp backend/.env.example backend/.env
```

Edit `backend/.env`:
```env
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_dev_stack
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_super_secret_jwt_key_here
OLLAMA_BASE_URL=http://localhost:11434
COMFYUI_BASE_URL=http://localhost:8188
AUGMENT_CODE_API_KEY=your_api_key_here
```

#### Frontend Environment
Copy and configure the frontend environment:
```bash
cp frontend/.env.example frontend/.env.local
```

Edit `frontend/.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

#### Docker Compose Environment
Create root `.env` file:
```env
POSTGRES_PASSWORD=postgres_dev_password
JWT_SECRET=your_super_secret_jwt_key_here
OLLAMA_BASE_URL=http://host.docker.internal:11434
COMFYUI_BASE_URL=http://host.docker.internal:8188
AUGMENT_CODE_API_KEY=your_api_key_here
```

### 2. Start Services

#### Using Docker Compose
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

#### Development Mode
```bash
# Start with development overrides
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Start with admin tools
docker-compose --profile tools up -d
```

### 3. Database Setup

#### Run Migrations
```bash
cd backend
npx prisma migrate dev
npx prisma generate
```

#### Seed Database
```bash
npm run db:seed
```

### 4. Verify Installation

#### Health Check
```bash
./scripts/health-check.sh
```

#### Manual Verification
- Backend: http://localhost:3001/health
- Frontend: http://localhost:3000
- API Status: http://localhost:3001/api/system/status

## Local Development Setup

### 1. Install Dependencies
```bash
# Root dependencies
npm install

# Backend dependencies
cd backend
npm install
cd ..

# Frontend dependencies
cd frontend
npm install
cd ..

# Shared dependencies
cd shared
npm install
cd ..
```

### 2. Start Development Servers

#### Backend
```bash
cd backend
npm run dev
```

#### Frontend
```bash
cd frontend
npm run dev
```

#### Database (Docker)
```bash
docker-compose up -d postgres redis
```

### 3. Development Tools

#### Database Admin
- **pgAdmin**: http://localhost:5050 (<EMAIL> / admin)
- **Prisma Studio**: `npx prisma studio`

#### Redis Admin
- **Redis Commander**: http://localhost:8081

## External Services Setup

### Ollama
1. Install Ollama: https://ollama.ai/download
2. Start Ollama service: `ollama serve`
3. Pull a model: `ollama pull llama2:7b`
4. Verify: http://localhost:11434/api/tags

### ComfyUI
1. Clone ComfyUI: `git clone https://github.com/comfyanonymous/ComfyUI.git`
2. Install dependencies: `pip install -r requirements.txt`
3. Start ComfyUI: `python main.py --listen`
4. Verify: http://localhost:8188

### Augment Code
1. Sign up for Augment Code API
2. Get your API key
3. Add to environment variables

## Troubleshooting

### Common Issues

#### Port Conflicts
If ports 3000, 3001, 5432, or 6379 are in use:
```bash
# Check what's using the port
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# Stop conflicting services or change ports in docker-compose.yml
```

#### Docker Issues
```bash
# Reset Docker
docker-compose down -v
docker system prune -a

# Rebuild images
docker-compose build --no-cache
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec postgres pg_isready -U postgres

# Reset database
docker-compose down postgres
docker volume rm ai-development-stack-gui_postgres_data
docker-compose up -d postgres
```

#### Permission Issues (Linux/macOS)
```bash
# Fix script permissions
chmod +x scripts/*.sh

# Fix file ownership
sudo chown -R $USER:$USER .
```

### Getting Help

1. **Check logs**: `docker-compose logs -f [service]`
2. **Run health check**: `./scripts/health-check.sh`
3. **Check service status**: `docker-compose ps`
4. **Restart services**: `docker-compose restart`

### Performance Optimization

#### For Development
- Increase Docker memory allocation to 4GB+
- Use SSD storage for better I/O performance
- Close unnecessary applications

#### For Production
- Use production Docker compose file
- Configure proper resource limits
- Set up monitoring and logging

## Next Steps

After successful setup:
1. **Explore the Interface**: Navigate through different sections
2. **Set up External Services**: Configure Ollama, ComfyUI, and Augment Code
3. **Create Your First Project**: Use the code editor to create a project
4. **Build a Workflow**: Try the ComfyUI workflow builder
5. **Chat with AI**: Test the Ollama chat interface

## Configuration Reference

### Environment Variables

#### Backend
| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3001` |
| `DATABASE_URL` | PostgreSQL connection string | - |
| `REDIS_URL` | Redis connection string | - |
| `JWT_SECRET` | JWT signing secret | - |
| `OLLAMA_BASE_URL` | Ollama service URL | `http://localhost:11434` |
| `COMFYUI_BASE_URL` | ComfyUI service URL | `http://localhost:8188` |
| `AUGMENT_CODE_API_KEY` | Augment Code API key | - |

#### Frontend
| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API URL | `http://localhost:3001` |
| `NEXT_PUBLIC_WS_URL` | WebSocket URL | `ws://localhost:3001` |

### Docker Compose Profiles

| Profile | Description | Services |
|---------|-------------|----------|
| Default | Core application | postgres, redis, backend, frontend |
| `tools` | Admin tools | + pgadmin, redis-commander |
| `dev-tools` | Development tools | + shared-watcher |

### Useful Commands

```bash
# Start specific services
docker-compose up -d postgres redis

# Scale services
docker-compose up -d --scale backend=2

# Update images
docker-compose pull
docker-compose up -d

# Backup database
docker-compose exec postgres pg_dump -U postgres ai_dev_stack > backup.sql

# Restore database
docker-compose exec -T postgres psql -U postgres ai_dev_stack < backup.sql
```
