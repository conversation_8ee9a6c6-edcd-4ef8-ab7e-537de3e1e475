#!/bin/bash

# Comprehensive Test Runner Script
# This script runs all types of tests for the AI Development Stack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
TEST_TYPE=${1:-all}
COVERAGE=${2:-false}
CI_MODE=${CI:-false}

# Test database setup
setup_test_db() {
    log_info "Setting up test database..."
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
        log_error "PostgreSQL is not running. Please start PostgreSQL first."
        exit 1
    fi
    
    # Create test database if it doesn't exist
    createdb ai_dev_stack_test 2>/dev/null || true
    
    # Set test environment variables
    export NODE_ENV=test
    export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ai_dev_stack_test"
    export TEST_DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ai_dev_stack_test"
    export JWT_SECRET="test-jwt-secret"
    export REDIS_URL="redis://localhost:6379/1"
    
    log_success "Test database setup complete"
}

# Backend unit tests
run_backend_tests() {
    log_info "Running backend unit tests..."
    
    cd backend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log_info "Installing backend dependencies..."
        npm ci
    fi
    
    # Run database migrations
    log_info "Running database migrations..."
    npx prisma migrate deploy
    npx prisma generate
    
    # Run tests
    if [ "$COVERAGE" = "true" ]; then
        npm run test:coverage
    else
        npm test
    fi
    
    cd ..
    log_success "Backend tests completed"
}

# Frontend unit tests
run_frontend_tests() {
    log_info "Running frontend unit tests..."
    
    cd frontend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log_info "Installing frontend dependencies..."
        npm ci
    fi
    
    # Run tests
    if [ "$COVERAGE" = "true" ]; then
        npm run test:coverage
    else
        npm test
    fi
    
    cd ..
    log_success "Frontend tests completed"
}

# Integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    cd backend
    
    # Run integration tests specifically
    npm run test:integration
    
    cd ..
    log_success "Integration tests completed"
}

# E2E tests
run_e2e_tests() {
    log_info "Running E2E tests..."
    
    # Start services if not in CI
    if [ "$CI_MODE" != "true" ]; then
        log_info "Starting services for E2E tests..."
        
        # Start database and Redis
        docker-compose up -d postgres redis
        
        # Wait for services to be ready
        sleep 10
        
        # Start backend
        cd backend
        npm run dev &
        BACKEND_PID=$!
        cd ..
        
        # Start frontend
        cd frontend
        npm run dev &
        FRONTEND_PID=$!
        cd ..
        
        # Wait for services to start
        log_info "Waiting for services to start..."
        sleep 30
        
        # Health check
        if ! curl -f http://localhost:3001/health > /dev/null 2>&1; then
            log_error "Backend health check failed"
            cleanup_e2e
            exit 1
        fi
        
        if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
            log_error "Frontend health check failed"
            cleanup_e2e
            exit 1
        fi
    fi
    
    # Run E2E tests
    cd frontend
    npx playwright test
    cd ..
    
    # Cleanup if not in CI
    if [ "$CI_MODE" != "true" ]; then
        cleanup_e2e
    fi
    
    log_success "E2E tests completed"
}

# Cleanup E2E test environment
cleanup_e2e() {
    log_info "Cleaning up E2E test environment..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Stop Docker services
    docker-compose down
    
    log_success "E2E cleanup completed"
}

# Lint and type checking
run_lint_and_types() {
    log_info "Running linting and type checking..."
    
    # Backend
    cd backend
    npm run lint
    npm run type-check
    cd ..
    
    # Frontend
    cd frontend
    npm run lint
    npm run type-check
    cd ..
    
    log_success "Linting and type checking completed"
}

# Security audit
run_security_audit() {
    log_info "Running security audit..."
    
    # Backend
    cd backend
    npm audit --audit-level=moderate
    cd ..
    
    # Frontend
    cd frontend
    npm audit --audit-level=moderate
    cd ..
    
    log_success "Security audit completed"
}

# Performance tests
run_performance_tests() {
    log_info "Running performance tests..."
    
    # This would include load testing, memory leak detection, etc.
    # For now, we'll just run a basic performance check
    
    cd frontend
    
    # Build the application
    npm run build
    
    # Analyze bundle size
    if command -v bundlesize > /dev/null; then
        bundlesize
    else
        log_warning "bundlesize not installed, skipping bundle analysis"
    fi
    
    cd ..
    
    log_success "Performance tests completed"
}

# Generate test reports
generate_reports() {
    log_info "Generating test reports..."
    
    # Create reports directory
    mkdir -p reports
    
    # Combine coverage reports if they exist
    if [ -d "backend/coverage" ] && [ -d "frontend/coverage" ]; then
        log_info "Combining coverage reports..."
        # This would require a tool like nyc or istanbul to combine reports
    fi
    
    # Copy test results
    if [ -d "frontend/test-results" ]; then
        cp -r frontend/test-results reports/e2e-results
    fi
    
    if [ -d "backend/coverage" ]; then
        cp -r backend/coverage reports/backend-coverage
    fi
    
    if [ -d "frontend/coverage" ]; then
        cp -r frontend/coverage reports/frontend-coverage
    fi
    
    log_success "Test reports generated in ./reports/"
}

# Main execution
main() {
    log_info "Starting test suite: $TEST_TYPE"
    
    # Setup
    setup_test_db
    
    # Run tests based on type
    case $TEST_TYPE in
        "unit")
            run_backend_tests
            run_frontend_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "lint")
            run_lint_and_types
            ;;
        "security")
            run_security_audit
            ;;
        "performance")
            run_performance_tests
            ;;
        "all")
            run_lint_and_types
            run_backend_tests
            run_frontend_tests
            run_integration_tests
            run_security_audit
            
            # Only run E2E tests if not in CI or if explicitly requested
            if [ "$CI_MODE" != "true" ] || [ "$RUN_E2E" = "true" ]; then
                run_e2e_tests
            fi
            
            run_performance_tests
            ;;
        *)
            log_error "Unknown test type: $TEST_TYPE"
            echo "Usage: $0 [unit|integration|e2e|lint|security|performance|all] [coverage]"
            exit 1
            ;;
    esac
    
    # Generate reports
    generate_reports
    
    log_success "All tests completed successfully!"
}

# Trap to ensure cleanup on exit
trap cleanup_e2e EXIT

# Run main function
main "$@"
