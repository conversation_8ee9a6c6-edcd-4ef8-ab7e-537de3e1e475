import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { getPrismaClient } from '@/utils/database';
import { userValidation } from '@shared/utils/validation';
import { ApiResponse, User } from '@shared/types';

const router = Router();
const prisma = getPrismaClient();

// Get current user profile
router.get('/profile', authenticate, asyncHandler(async (req, res) => {
  const response: ApiResponse<{ user: User }> = {
    success: true,
    data: {
      user: req.user!,
    },
  };

  res.json(response);
}));

// Update user profile
router.put('/profile', authenticate, asyncHandler(async (req, res) => {
  const validatedData = userValidation.updateProfile.parse(req.body);

  const updatedUser = await prisma.user.update({
    where: { id: req.user!.id },
    data: validatedData,
    select: {
      id: true,
      email: true,
      username: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  const response: ApiResponse<{ user: User }> = {
    success: true,
    data: {
      user: updatedUser,
    },
    message: 'Profile updated successfully',
  };

  res.json(response);
}));

// Delete user account
router.delete('/account', authenticate, asyncHandler(async (req, res) => {
  // Delete user and all related data (cascade delete)
  await prisma.user.delete({
    where: { id: req.user!.id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Account deleted successfully',
  };

  res.json(response);
}));

export default router;
