{"name": "@ai-dev-stack/shared", "version": "1.0.0", "description": "Shared types and utilities for AI Development Stack GUI", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "exports": {".": "./index.ts", "./types": "./types/index.ts", "./utils": "./utils/index.ts"}}