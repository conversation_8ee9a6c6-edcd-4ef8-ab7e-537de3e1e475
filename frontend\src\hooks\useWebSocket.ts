import { useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { WS_EVENTS } from '@shared/utils/constants';
import { useAuth } from './useAuth';

interface UseWebSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  sendMessage: (event: string, data: any) => void;
  addEventListener: (event: string, handler: (data: any) => void) => () => void;
}

export function useWebSocket(): UseWebSocketReturn {
  const socketRef = useRef<Socket | null>(null);
  const { tokens, isAuthenticated } = useAuth();
  const listenersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map());

  // Initialize WebSocket connection
  useEffect(() => {
    if (!isAuthenticated || !tokens?.accessToken) {
      return;
    }

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';
    
    const socket = io(wsUrl, {
      auth: {
        token: tokens.accessToken,
      },
      transports: ['websocket', 'polling'],
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on('connect', () => {
      console.log('WebSocket connected');
    });

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
    });

    // Authentication confirmation
    socket.on(WS_EVENTS.AUTHENTICATED, (data) => {
      console.log('WebSocket authenticated:', data);
    });

    // Set up event listeners for all registered events
    listenersRef.current.forEach((handlers, event) => {
      handlers.forEach((handler) => {
        socket.on(event, handler);
      });
    });

    // Cleanup on unmount or auth change
    return () => {
      socket.disconnect();
      socketRef.current = null;
    };
  }, [isAuthenticated, tokens?.accessToken]);

  // Send message through WebSocket
  const sendMessage = useCallback((event: string, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot send message:', event, data);
    }
  }, []);

  // Add event listener
  const addEventListener = useCallback((event: string, handler: (data: any) => void) => {
    // Add to listeners map
    if (!listenersRef.current.has(event)) {
      listenersRef.current.set(event, new Set());
    }
    listenersRef.current.get(event)!.add(handler);

    // Add to socket if connected
    if (socketRef.current) {
      socketRef.current.on(event, handler);
    }

    // Return cleanup function
    return () => {
      // Remove from listeners map
      const handlers = listenersRef.current.get(event);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          listenersRef.current.delete(event);
        }
      }

      // Remove from socket if connected
      if (socketRef.current) {
        socketRef.current.off(event, handler);
      }
    };
  }, []);

  return {
    socket: socketRef.current,
    isConnected: socketRef.current?.connected || false,
    sendMessage,
    addEventListener,
  };
}

// Specific hooks for common WebSocket operations

export function useChatWebSocket() {
  const { sendMessage, addEventListener } = useWebSocket();

  const sendChatMessage = useCallback((data: {
    message: string;
    model: string;
    conversationId?: string;
    stream?: boolean;
  }) => {
    sendMessage(WS_EVENTS.CHAT_MESSAGE, data);
  }, [sendMessage]);

  const onChatStream = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.CHAT_STREAM, handler);
  }, [addEventListener]);

  const onChatError = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.CHAT_ERROR, handler);
  }, [addEventListener]);

  return {
    sendChatMessage,
    onChatStream,
    onChatError,
  };
}

export function useWorkflowWebSocket() {
  const { sendMessage, addEventListener } = useWebSocket();

  const executeWorkflow = useCallback((data: {
    workflowId: string;
    inputs?: any;
  }) => {
    sendMessage(WS_EVENTS.WORKFLOW_EXECUTE, data);
  }, [sendMessage]);

  const onWorkflowStarted = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.WORKFLOW_STARTED, handler);
  }, [addEventListener]);

  const onWorkflowProgress = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.WORKFLOW_PROGRESS, handler);
  }, [addEventListener]);

  const onGenerationComplete = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.GENERATION_COMPLETE, handler);
  }, [addEventListener]);

  const onWorkflowError = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.WORKFLOW_ERROR, handler);
  }, [addEventListener]);

  return {
    executeWorkflow,
    onWorkflowStarted,
    onWorkflowProgress,
    onGenerationComplete,
    onWorkflowError,
  };
}

export function useCodeWebSocket() {
  const { sendMessage, addEventListener } = useWebSocket();

  const analyzeCode = useCallback((data: {
    code: string;
    language: string;
    filePath?: string;
  }) => {
    sendMessage(WS_EVENTS.CODE_ANALYZE, data);
  }, [sendMessage]);

  const onCodeAnalysisResult = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.CODE_ANALYSIS_RESULT, handler);
  }, [addEventListener]);

  const onCodeError = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.CODE_ERROR, handler);
  }, [addEventListener]);

  return {
    analyzeCode,
    onCodeAnalysisResult,
    onCodeError,
  };
}

export function useSystemWebSocket() {
  const { sendMessage, addEventListener } = useWebSocket();

  const subscribeToMetrics = useCallback(() => {
    sendMessage(WS_EVENTS.SUBSCRIBE_METRICS, {});
  }, [sendMessage]);

  const unsubscribeFromMetrics = useCallback(() => {
    sendMessage(WS_EVENTS.UNSUBSCRIBE_METRICS, {});
  }, [sendMessage]);

  const onMetricsUpdate = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.METRICS_UPDATE, handler);
  }, [addEventListener]);

  const onSystemStatus = useCallback((handler: (data: any) => void) => {
    return addEventListener(WS_EVENTS.SYSTEM_STATUS, handler);
  }, [addEventListener]);

  return {
    subscribeToMetrics,
    unsubscribeFromMetrics,
    onMetricsUpdate,
    onSystemStatus,
  };
}
