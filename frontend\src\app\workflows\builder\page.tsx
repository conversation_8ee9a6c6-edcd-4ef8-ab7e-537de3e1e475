'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WorkflowBuilder } from '@/components/workflow/workflow-builder';
import { comfyuiApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function WorkflowBuilderPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const workflowId = searchParams.get('id');
  const queryClient = useQueryClient();

  // Fetch existing workflow if editing
  const { data: workflowData, isLoading } = useQuery({
    queryKey: ['comfyui', 'workflow', workflowId],
    queryFn: () => workflowId ? comfyuiApi.getWorkflow(workflowId) : null,
    enabled: !!workflowId,
  });

  // Create workflow mutation
  const createWorkflowMutation = useMutation({
    mutationFn: comfyuiApi.createWorkflow,
    onSuccess: (data) => {
      toast.success('Workflow created successfully');
      queryClient.invalidateQueries({ queryKey: ['comfyui', 'workflows'] });
      router.push(`/workflows/builder?id=${data.workflow.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create workflow');
    },
  });

  // Update workflow mutation
  const updateWorkflowMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      comfyuiApi.updateWorkflow(id, data),
    onSuccess: () => {
      toast.success('Workflow saved successfully');
      queryClient.invalidateQueries({ queryKey: ['comfyui', 'workflows'] });
      queryClient.invalidateQueries({ queryKey: ['comfyui', 'workflow', workflowId] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save workflow');
    },
  });

  // Execute workflow mutation
  const executeWorkflowMutation = useMutation({
    mutationFn: comfyuiApi.executeWorkflow,
    onSuccess: () => {
      toast.success('Workflow execution started');
      router.push('/workflows');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to execute workflow');
    },
  });

  const handleSaveWorkflow = (workflowData: any) => {
    if (workflowId) {
      updateWorkflowMutation.mutate({
        id: workflowId,
        data: workflowData,
      });
    } else {
      createWorkflowMutation.mutate(workflowData);
    }
  };

  const handleExecuteWorkflow = (workflowData: any) => {
    if (workflowId) {
      executeWorkflowMutation.mutate({ workflowId });
    } else {
      // Save first, then execute
      createWorkflowMutation.mutate(workflowData, {
        onSuccess: (data) => {
          executeWorkflowMutation.mutate({ workflowId: data.workflow.id });
        },
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <WorkflowBuilder
        workflow={workflowData?.data?.workflow}
        onSave={handleSaveWorkflow}
        onExecute={handleExecuteWorkflow}
      />
    </div>
  );
}
