import request from 'supertest';
import { app } from '../app';
import { getPrismaClient } from '../utils/database';
import { ComfyUIService } from '../services/comfyuiService';
import bcrypt from 'bcryptjs';

// Mock the ComfyUIService
jest.mock('../services/comfyuiService');
const MockedComfyUIService = ComfyUIService as jest.MockedClass<typeof ComfyUIService>;

const prisma = getPrismaClient();

describe('ComfyUI API', () => {
  let accessToken: string;
  let userId: string;

  beforeAll(async () => {
    // Create a test user and get access token
    const hashedPassword = await bcrypt.hash('Test123!@#', 12);
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
      },
    });

    userId = user.id;

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Test123!@#',
      });

    accessToken = loginResponse.body.data.tokens.accessToken;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await prisma.queueItem.deleteMany();
    await prisma.workflow.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('GET /api/comfyui/status', () => {
    it('should get ComfyUI status successfully', async () => {
      const mockStatus = {
        status: 'online',
        queue_remaining: 2,
        queue_running: 1,
      };

      MockedComfyUIService.prototype.getStatus = jest.fn().mockResolvedValue(mockStatus);

      const response = await request(app)
        .get('/api/comfyui/status')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('online');
      expect(response.body.data.queue_remaining).toBe(2);
    });

    it('should handle service unavailable', async () => {
      MockedComfyUIService.prototype.getStatus = jest.fn().mockRejectedValue(
        new Error('Service unavailable')
      );

      const response = await request(app)
        .get('/api/comfyui/status')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(503);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/comfyui/workflows', () => {
    beforeEach(async () => {
      await prisma.workflow.deleteMany();
    });

    it('should list workflows successfully', async () => {
      // Create test workflows
      const workflow1 = await prisma.workflow.create({
        data: {
          name: 'Text to Image',
          description: 'Basic text to image generation',
          nodes: { '1': { type: 'CLIPTextEncode' } },
          connections: [],
          userId,
        },
      });

      const workflow2 = await prisma.workflow.create({
        data: {
          name: 'Image to Image',
          description: 'Image transformation workflow',
          nodes: { '1': { type: 'LoadImage' } },
          connections: [],
          userId,
        },
      });

      const response = await request(app)
        .get('/api/comfyui/workflows')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(2);
    });

    it('should support pagination and search', async () => {
      // Create multiple workflows
      for (let i = 1; i <= 5; i++) {
        await prisma.workflow.create({
          data: {
            name: `Workflow ${i}`,
            description: `Description ${i}`,
            nodes: {},
            connections: [],
            userId,
          },
        });
      }

      const response = await request(app)
        .get('/api/comfyui/workflows?page=1&limit=3&search=Workflow')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(3);
      expect(response.body.data.pagination.total).toBe(5);
    });
  });

  describe('POST /api/comfyui/workflows', () => {
    it('should create workflow successfully', async () => {
      const workflowData = {
        name: 'New Workflow',
        description: 'Test workflow',
        nodes: {
          '1': {
            type: 'CLIPTextEncode',
            inputs: { text: 'test prompt' },
          },
        },
        connections: [],
      };

      const response = await request(app)
        .post('/api/comfyui/workflows')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(workflowData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workflow.name).toBe(workflowData.name);
      expect(response.body.data.workflow.userId).toBe(userId);
    });

    it('should validate workflow data', async () => {
      const response = await request(app)
        .post('/api/comfyui/workflows')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({}) // Missing required fields
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/comfyui/workflows/:id', () => {
    let workflowId: string;

    beforeEach(async () => {
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          description: 'Test description',
          nodes: { '1': { type: 'CLIPTextEncode' } },
          connections: [],
          userId,
        },
      });
      workflowId = workflow.id;
    });

    afterEach(async () => {
      await prisma.workflow.deleteMany();
    });

    it('should get workflow successfully', async () => {
      const response = await request(app)
        .get(`/api/comfyui/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workflow.id).toBe(workflowId);
      expect(response.body.data.workflow.name).toBe('Test Workflow');
    });

    it('should return 404 for non-existent workflow', async () => {
      const response = await request(app)
        .get('/api/comfyui/workflows/non-existent-id')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/comfyui/workflows/:id', () => {
    let workflowId: string;

    beforeEach(async () => {
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          description: 'Test description',
          nodes: { '1': { type: 'CLIPTextEncode' } },
          connections: [],
          userId,
        },
      });
      workflowId = workflow.id;
    });

    afterEach(async () => {
      await prisma.workflow.deleteMany();
    });

    it('should update workflow successfully', async () => {
      const updateData = {
        name: 'Updated Workflow',
        description: 'Updated description',
      };

      const response = await request(app)
        .put(`/api/comfyui/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workflow.name).toBe(updateData.name);
      expect(response.body.data.workflow.description).toBe(updateData.description);
    });

    it('should validate update data', async () => {
      const response = await request(app)
        .put(`/api/comfyui/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ name: '' }) // Invalid name
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/comfyui/workflows/:id', () => {
    let workflowId: string;

    beforeEach(async () => {
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          description: 'Test description',
          nodes: { '1': { type: 'CLIPTextEncode' } },
          connections: [],
          userId,
        },
      });
      workflowId = workflow.id;
    });

    it('should delete workflow successfully', async () => {
      const response = await request(app)
        .delete(`/api/comfyui/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);

      // Verify workflow is deleted
      const deletedWorkflow = await prisma.workflow.findUnique({
        where: { id: workflowId },
      });
      expect(deletedWorkflow).toBeNull();
    });
  });

  describe('POST /api/comfyui/workflows/:id/execute', () => {
    let workflowId: string;

    beforeEach(async () => {
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          description: 'Test description',
          nodes: {
            '1': {
              type: 'CLIPTextEncode',
              inputs: { text: 'test prompt' },
            },
          },
          connections: [],
          userId,
        },
      });
      workflowId = workflow.id;
    });

    afterEach(async () => {
      await prisma.queueItem.deleteMany();
      await prisma.workflow.deleteMany();
    });

    it('should execute workflow successfully', async () => {
      const mockExecution = {
        prompt_id: 'test-prompt-id',
        number: 1,
      };

      MockedComfyUIService.prototype.executeWorkflow = jest.fn().mockResolvedValue(mockExecution);
      MockedComfyUIService.prototype.convertWorkflowToAPI = jest.fn().mockReturnValue({
        '1': {
          class_type: 'CLIPTextEncode',
          inputs: { text: 'test prompt' },
        },
      });

      const response = await request(app)
        .post(`/api/comfyui/workflows/${workflowId}/execute`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ inputs: {} })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.execution.prompt_id).toBe('test-prompt-id');

      // Verify queue item was created
      const queueItem = await prisma.queueItem.findFirst({
        where: { workflowId },
      });
      expect(queueItem).toBeTruthy();
      expect(queueItem?.status).toBe('PENDING');
    });

    it('should handle execution errors', async () => {
      MockedComfyUIService.prototype.executeWorkflow = jest.fn().mockRejectedValue(
        new Error('Execution failed')
      );
      MockedComfyUIService.prototype.convertWorkflowToAPI = jest.fn().mockReturnValue({});

      const response = await request(app)
        .post(`/api/comfyui/workflows/${workflowId}/execute`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ inputs: {} })
        .expect(500);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/comfyui/queue', () => {
    beforeEach(async () => {
      await prisma.queueItem.deleteMany();
    });

    it('should get queue items successfully', async () => {
      // Create test workflow
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          description: 'Test description',
          nodes: {},
          connections: [],
          userId,
        },
      });

      // Create test queue items
      await prisma.queueItem.createMany({
        data: [
          {
            workflowId: workflow.id,
            status: 'PENDING',
            userId,
          },
          {
            workflowId: workflow.id,
            status: 'RUNNING',
            progress: 50,
            userId,
          },
        ],
      });

      const response = await request(app)
        .get('/api/comfyui/queue')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(2);
    });

    it('should filter queue items by status', async () => {
      const workflow = await prisma.workflow.create({
        data: {
          name: 'Test Workflow',
          nodes: {},
          connections: [],
          userId,
        },
      });

      await prisma.queueItem.createMany({
        data: [
          { workflowId: workflow.id, status: 'PENDING', userId },
          { workflowId: workflow.id, status: 'RUNNING', userId },
          { workflowId: workflow.id, status: 'COMPLETED', userId },
        ],
      });

      const response = await request(app)
        .get('/api/comfyui/queue?status=PENDING')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].status).toBe('PENDING');
    });
  });

  describe('GET /api/comfyui/nodes', () => {
    it('should get available nodes successfully', async () => {
      const mockNodes = {
        CLIPTextEncode: {
          input: {
            required: {
              text: ['STRING', { multiline: true }],
              clip: ['CLIP'],
            },
          },
          output: ['CONDITIONING'],
          output_is_list: [false],
          output_name: ['CONDITIONING'],
          name: 'CLIPTextEncode',
          display_name: 'CLIP Text Encode (Prompt)',
          description: 'Encode text using CLIP',
          category: 'conditioning',
        },
      };

      MockedComfyUIService.prototype.getObjectInfo = jest.fn().mockResolvedValue(mockNodes);

      const response = await request(app)
        .get('/api/comfyui/nodes')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.nodes).toHaveProperty('CLIPTextEncode');
      expect(response.body.data.nodes.CLIPTextEncode.category).toBe('conditioning');
    });

    it('should handle service errors', async () => {
      MockedComfyUIService.prototype.getObjectInfo = jest.fn().mockRejectedValue(
        new Error('Service unavailable')
      );

      const response = await request(app)
        .get('/api/comfyui/nodes')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(503);

      expect(response.body.success).toBe(false);
    });
  });
});
