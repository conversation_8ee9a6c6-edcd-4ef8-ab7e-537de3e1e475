'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bug, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  TrendingUp,
  Clock,
  FileText,
  Zap,
  Shield,
  Target
} from 'lucide-react';
import { useCodeWebSocket } from '@/hooks/useWebSocket';
import { cn } from '@/lib/utils';

interface CodeIssue {
  id: string;
  type: 'error' | 'warning' | 'info' | 'suggestion';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  line: number;
  column: number;
  rule?: string;
  category: string;
}

interface CodeMetrics {
  complexity: number;
  maintainability: number;
  readability: number;
  performance: number;
  security: number;
  testCoverage?: number;
  linesOfCode: number;
  functions: number;
  classes: number;
}

interface CodeAnalysisProps {
  code: string;
  language: string;
  filePath?: string;
  className?: string;
}

export function CodeAnalysis({ code, language, filePath, className }: CodeAnalysisProps) {
  const [issues, setIssues] = useState<CodeIssue[]>([]);
  const [metrics, setMetrics] = useState<CodeMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastAnalyzed, setLastAnalyzed] = useState<Date | null>(null);
  
  const { analyzeCode, onCodeAnalysisResult, onCodeError } = useCodeWebSocket();

  // Listen for analysis results
  useEffect(() => {
    const unsubscribeResult = onCodeAnalysisResult((data) => {
      setIsLoading(false);
      setLastAnalyzed(new Date());
      
      if (data.analysis) {
        setIssues(data.analysis.issues || []);
        setMetrics(data.analysis.metrics || null);
      }
    });

    const unsubscribeError = onCodeError((data) => {
      setIsLoading(false);
      console.error('Code analysis error:', data.error);
    });

    return () => {
      unsubscribeResult();
      unsubscribeError();
    };
  }, [onCodeAnalysisResult, onCodeError]);

  // Analyze code when it changes
  useEffect(() => {
    if (code.trim() && language) {
      setIsLoading(true);
      analyzeCode({ code, language, filePath });
    }
  }, [code, language, filePath, analyzeCode]);

  const getIssueIcon = (type: CodeIssue['type']) => {
    switch (type) {
      case 'error':
        return <Bug className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'suggestion':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: CodeIssue['severity']) => {
    switch (severity) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getMetricColor = (value: number) => {
    if (value >= 80) return 'text-green-600';
    if (value >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'complexity':
        return <Target className="h-4 w-4" />;
      case 'maintainability':
        return <TrendingUp className="h-4 w-4" />;
      case 'readability':
        return <FileText className="h-4 w-4" />;
      case 'performance':
        return <Zap className="h-4 w-4" />;
      case 'security':
        return <Shield className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const issuesByType = issues.reduce((acc, issue) => {
    acc[issue.type] = (acc[issue.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Code Analysis
          </CardTitle>
          <CardDescription>
            Analyzing your code for issues and metrics...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Code Analysis
          {lastAnalyzed && (
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {lastAnalyzed.toLocaleTimeString()}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Code quality analysis and metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="issues" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="issues">
              Issues ({issues.length})
            </TabsTrigger>
            <TabsTrigger value="metrics">
              Metrics
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="issues" className="space-y-4">
            {/* Issue Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {issuesByType.error || 0}
                </div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {issuesByType.warning || 0}
                </div>
                <div className="text-sm text-muted-foreground">Warnings</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {issuesByType.info || 0}
                </div>
                <div className="text-sm text-muted-foreground">Info</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {issuesByType.suggestion || 0}
                </div>
                <div className="text-sm text-muted-foreground">Suggestions</div>
              </div>
            </div>

            {/* Issues List */}
            {issues.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <p>No issues found</p>
                <p className="text-sm">Your code looks good!</p>
              </div>
            ) : (
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {issues.map((issue) => (
                    <div
                      key={issue.id}
                      className="p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getIssueIcon(issue.type)}
                          <h4 className="font-medium text-sm">{issue.title}</h4>
                        </div>
                        <Badge 
                          variant="outline" 
                          className={cn('text-xs', getSeverityColor(issue.severity))}
                        >
                          {issue.severity}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        {issue.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Line {issue.line}, Column {issue.column}</span>
                        <div className="flex gap-2">
                          <Badge variant="outline" className="text-xs">
                            {issue.category}
                          </Badge>
                          {issue.rule && (
                            <Badge variant="outline" className="text-xs">
                              {issue.rule}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
          
          <TabsContent value="metrics" className="space-y-4">
            {metrics ? (
              <>
                {/* Quality Metrics */}
                <div className="grid gap-4">
                  {[
                    { key: 'complexity', label: 'Complexity', value: metrics.complexity },
                    { key: 'maintainability', label: 'Maintainability', value: metrics.maintainability },
                    { key: 'readability', label: 'Readability', value: metrics.readability },
                    { key: 'performance', label: 'Performance', value: metrics.performance },
                    { key: 'security', label: 'Security', value: metrics.security },
                  ].map((metric) => (
                    <div key={metric.key} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getMetricIcon(metric.key)}
                          <span className="text-sm font-medium">{metric.label}</span>
                        </div>
                        <span className={cn('text-sm font-bold', getMetricColor(metric.value))}>
                          {metric.value}%
                        </span>
                      </div>
                      <Progress 
                        value={metric.value} 
                        className="h-2"
                        indicatorClassName={
                          metric.value >= 80 ? 'bg-green-500' :
                          metric.value >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }
                      />
                    </div>
                  ))}
                </div>

                {/* Code Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.linesOfCode}</div>
                    <div className="text-sm text-muted-foreground">Lines of Code</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.functions}</div>
                    <div className="text-sm text-muted-foreground">Functions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.classes}</div>
                    <div className="text-sm text-muted-foreground">Classes</div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No metrics available</p>
                <p className="text-sm">Metrics will appear after code analysis</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
