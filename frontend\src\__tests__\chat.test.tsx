import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ChatPage from '@/app/chat/page';
import { ollamaApi } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api', () => ({
  ollamaApi: {
    getConversations: jest.fn(),
    getConversation: jest.fn(),
    createConversation: jest.fn(),
    sendMessage: jest.fn(),
    listModels: jest.fn(),
  },
}));

// Mock WebSocket hooks
jest.mock('@/hooks/useWebSocket', () => ({
  useChatWebSocket: () => ({
    sendChatMessage: jest.fn(),
    onChatStream: jest.fn(() => () => {}),
    onChatError: jest.fn(() => () => {}),
  }),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Chat Interface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock default API responses
    (ollamaApi.getConversations as jest.Mock).mockResolvedValue({
      data: {
        items: [],
        pagination: { page: 1, limit: 20, total: 0 },
      },
    });

    (ollamaApi.listModels as jest.Mock).mockResolvedValue({
      data: {
        models: [
          {
            name: 'llama2:7b',
            size: **********,
            digest: 'sha256:abc123',
            modified_at: '2024-01-01T00:00:00Z',
          },
        ],
      },
    });
  });

  describe('Chat Page', () => {
    it('renders chat interface correctly', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText(/chat with ai/i)).toBeInTheDocument();
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
      });
    });

    it('displays model selector', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText(/select model/i)).toBeInTheDocument();
      });

      // Click model selector
      const modelSelector = screen.getByText(/select model/i);
      fireEvent.click(modelSelector);

      await waitFor(() => {
        expect(screen.getByText(/llama2:7b/i)).toBeInTheDocument();
      });
    });

    it('shows conversation list when available', async () => {
      const mockConversations = [
        {
          id: '1',
          title: 'Test Conversation 1',
          model: 'llama2:7b',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          title: 'Test Conversation 2',
          model: 'llama2:7b',
          updatedAt: '2024-01-01T01:00:00Z',
        },
      ];

      (ollamaApi.getConversations as jest.Mock).mockResolvedValue({
        data: {
          items: mockConversations,
          pagination: { page: 1, limit: 20, total: 2 },
        },
      });

      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
        expect(screen.getByText('Test Conversation 2')).toBeInTheDocument();
      });
    });

    it('creates new conversation when starting chat', async () => {
      const mockNewConversation = {
        id: 'new-conversation-id',
        title: 'New Conversation',
        model: 'llama2:7b',
        messages: [],
      };

      (ollamaApi.createConversation as jest.Mock).mockResolvedValue({
        data: { conversation: mockNewConversation },
      });

      renderWithProviders(<ChatPage />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      // Select model first
      const modelSelector = screen.getByText(/select model/i);
      fireEvent.click(modelSelector);
      
      await waitFor(() => {
        const modelOption = screen.getByText(/llama2:7b/i);
        fireEvent.click(modelOption);
      });

      // Type message
      const messageInput = screen.getByPlaceholderText(/type your message/i);
      fireEvent.change(messageInput, { target: { value: 'Hello, AI!' } });

      // Send message
      const sendButton = screen.getByRole('button', { name: /send/i });
      fireEvent.click(sendButton);

      await waitFor(() => {
        expect(ollamaApi.createConversation).toHaveBeenCalledWith({
          title: 'Hello, AI!',
          model: 'llama2:7b',
        });
      });
    });

    it('validates message input', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      // Try to send empty message
      const sendButton = screen.getByRole('button', { name: /send/i });
      fireEvent.click(sendButton);

      // Should not create conversation or send message
      expect(ollamaApi.createConversation).not.toHaveBeenCalled();
      expect(ollamaApi.sendMessage).not.toHaveBeenCalled();
    });

    it('displays chat messages correctly', async () => {
      const mockConversation = {
        id: 'conversation-id',
        title: 'Test Conversation',
        model: 'llama2:7b',
        messages: [
          {
            id: '1',
            role: 'user',
            content: 'Hello, how are you?',
            createdAt: '2024-01-01T00:00:00Z',
          },
          {
            id: '2',
            role: 'assistant',
            content: 'Hello! I am doing well, thank you for asking. How can I help you today?',
            createdAt: '2024-01-01T00:01:00Z',
          },
        ],
      };

      (ollamaApi.getConversation as jest.Mock).mockResolvedValue({
        data: { conversation: mockConversation },
      });

      // Mock URL search params to simulate selecting a conversation
      const mockSearchParams = new URLSearchParams('?conversation=conversation-id');
      jest.mocked(require('next/navigation').useSearchParams).mockReturnValue(mockSearchParams);

      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText('Hello, how are you?')).toBeInTheDocument();
        expect(screen.getByText(/Hello! I am doing well/)).toBeInTheDocument();
      });

      // Check message roles are displayed correctly
      const userMessage = screen.getByText('Hello, how are you?').closest('[data-role="user"]');
      const assistantMessage = screen.getByText(/Hello! I am doing well/).closest('[data-role="assistant"]');
      
      expect(userMessage).toBeInTheDocument();
      expect(assistantMessage).toBeInTheDocument();
    });

    it('handles message sending errors', async () => {
      (ollamaApi.sendMessage as jest.Mock).mockRejectedValue(new Error('Network error'));

      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      // Select model
      const modelSelector = screen.getByText(/select model/i);
      fireEvent.click(modelSelector);
      
      await waitFor(() => {
        const modelOption = screen.getByText(/llama2:7b/i);
        fireEvent.click(modelOption);
      });

      // Type and send message
      const messageInput = screen.getByPlaceholderText(/type your message/i);
      fireEvent.change(messageInput, { target: { value: 'Test message' } });

      const sendButton = screen.getByRole('button', { name: /send/i });
      fireEvent.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to send message/i)).toBeInTheDocument();
      });
    });

    it('supports keyboard shortcuts', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      const messageInput = screen.getByPlaceholderText(/type your message/i);
      
      // Type message
      fireEvent.change(messageInput, { target: { value: 'Test message' } });

      // Press Enter to send
      fireEvent.keyDown(messageInput, { key: 'Enter', code: 'Enter' });

      // Should attempt to create conversation (since no model selected, it might show validation)
      await waitFor(() => {
        // The exact behavior depends on implementation, but Enter should trigger send
        expect(messageInput.value).toBe(''); // Input should be cleared after send attempt
      });
    });

    it('supports Shift+Enter for new line', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      const messageInput = screen.getByPlaceholderText(/type your message/i);
      
      // Type message
      fireEvent.change(messageInput, { target: { value: 'Line 1' } });

      // Press Shift+Enter for new line
      fireEvent.keyDown(messageInput, { 
        key: 'Enter', 
        code: 'Enter', 
        shiftKey: true 
      });

      // Should not send message, should add new line
      expect(ollamaApi.createConversation).not.toHaveBeenCalled();
    });

    it('shows typing indicator during message processing', async () => {
      // Mock a delayed response
      (ollamaApi.sendMessage as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 1000))
      );

      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
      });

      // Select model and send message
      const modelSelector = screen.getByText(/select model/i);
      fireEvent.click(modelSelector);
      
      await waitFor(() => {
        const modelOption = screen.getByText(/llama2:7b/i);
        fireEvent.click(modelOption);
      });

      const messageInput = screen.getByPlaceholderText(/type your message/i);
      fireEvent.change(messageInput, { target: { value: 'Test message' } });

      const sendButton = screen.getByRole('button', { name: /send/i });
      fireEvent.click(sendButton);

      // Should show loading state
      await waitFor(() => {
        expect(screen.getByText(/thinking/i) || screen.getByText(/typing/i)).toBeInTheDocument();
      });
    });
  });

  describe('Conversation Management', () => {
    it('allows creating new conversation', async () => {
      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText(/new conversation/i)).toBeInTheDocument();
      });

      const newConversationButton = screen.getByText(/new conversation/i);
      fireEvent.click(newConversationButton);

      // Should clear current conversation and reset interface
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    it('allows deleting conversations', async () => {
      const mockConversations = [
        {
          id: '1',
          title: 'Test Conversation',
          model: 'llama2:7b',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ];

      (ollamaApi.getConversations as jest.Mock).mockResolvedValue({
        data: {
          items: mockConversations,
          pagination: { page: 1, limit: 20, total: 1 },
        },
      });

      renderWithProviders(<ChatPage />);

      await waitFor(() => {
        expect(screen.getByText('Test Conversation')).toBeInTheDocument();
      });

      // Find and click delete button (this depends on your UI implementation)
      const deleteButton = screen.getByRole('button', { name: /delete/i });
      fireEvent.click(deleteButton);

      // Should show confirmation dialog
      await waitFor(() => {
        expect(screen.getByText(/are you sure/i)).toBeInTheDocument();
      });
    });
  });
});
