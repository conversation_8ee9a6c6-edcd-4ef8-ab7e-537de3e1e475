import { Router } from 'express';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { getPrismaClient } from '@/utils/database';
import { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken,
  authenticate 
} from '@/middleware/auth';
import { authRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, ValidationError, AuthenticationError, ConflictError } from '@/middleware/errorHandler';
import { logInfo, logSecurity } from '@/utils/logger';
import { userValidation } from '@shared/utils/validation';
import { ApiResponse, AuthTokens, User } from '@shared/types';

const router = Router();
const prisma = getPrismaClient();

// Register user
router.post('/register', authRateLimiter, asyncHandler(async (req, res) => {
  // Validate input
  const validatedData = userValidation.register.parse(req.body);
  
  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email: validatedData.email },
        { username: validatedData.username },
      ],
    },
  });

  if (existingUser) {
    if (existingUser.email === validatedData.email) {
      throw new ConflictError('Email already registered');
    } else {
      throw new ConflictError('Username already taken');
    }
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(validatedData.password, 12);

  // Create user
  const user = await prisma.user.create({
    data: {
      email: validatedData.email,
      username: validatedData.username,
      password: hashedPassword,
      firstName: validatedData.firstName,
      lastName: validatedData.lastName,
    },
    select: {
      id: true,
      email: true,
      username: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  // Generate tokens
  const accessToken = generateToken(user);
  const refreshToken = generateRefreshToken(user);

  // Store refresh token
  await prisma.refreshToken.create({
    data: {
      token: refreshToken,
      userId: user.id,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  });

  logInfo('User registered successfully', { userId: user.id, email: user.email });

  const response: ApiResponse<{ user: User; tokens: AuthTokens }> = {
    success: true,
    data: {
      user,
      tokens: {
        accessToken,
        refreshToken,
      },
    },
    message: 'User registered successfully',
  };

  res.status(201).json(response);
}));

// Login user
router.post('/login', authRateLimiter, asyncHandler(async (req, res) => {
  // Validate input
  const validatedData = userValidation.login.parse(req.body);

  // Find user
  const user = await prisma.user.findUnique({
    where: { email: validatedData.email },
  });

  if (!user) {
    logSecurity('Login failed: User not found', undefined, req.ip, { email: validatedData.email });
    throw new AuthenticationError('Invalid credentials');
  }

  if (!user.isActive) {
    logSecurity('Login failed: User inactive', user.id, req.ip);
    throw new AuthenticationError('Account is inactive');
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(validatedData.password, user.password);
  
  if (!isPasswordValid) {
    logSecurity('Login failed: Invalid password', user.id, req.ip);
    throw new AuthenticationError('Invalid credentials');
  }

  // Generate tokens
  const accessToken = generateToken(user);
  const refreshToken = generateRefreshToken(user);

  // Store refresh token
  await prisma.refreshToken.create({
    data: {
      token: refreshToken,
      userId: user.id,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  });

  // Remove password from response
  const { password, ...userWithoutPassword } = user;

  logInfo('User logged in successfully', { userId: user.id, email: user.email });

  const response: ApiResponse<{ user: User; tokens: AuthTokens }> = {
    success: true,
    data: {
      user: userWithoutPassword,
      tokens: {
        accessToken,
        refreshToken,
      },
    },
    message: 'Login successful',
  };

  res.json(response);
}));

// Refresh token
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new AuthenticationError('Refresh token required');
  }

  // Verify refresh token
  const payload = verifyRefreshToken(refreshToken);

  // Check if refresh token exists in database
  const storedToken = await prisma.refreshToken.findUnique({
    where: { token: refreshToken },
    include: { user: true },
  });

  if (!storedToken || storedToken.expiresAt < new Date()) {
    throw new AuthenticationError('Invalid or expired refresh token');
  }

  if (!storedToken.user.isActive) {
    throw new AuthenticationError('Account is inactive');
  }

  // Generate new tokens
  const newAccessToken = generateToken(storedToken.user);
  const newRefreshToken = generateRefreshToken(storedToken.user);

  // Update refresh token in database
  await prisma.refreshToken.update({
    where: { id: storedToken.id },
    data: {
      token: newRefreshToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  });

  const response: ApiResponse<{ tokens: AuthTokens }> = {
    success: true,
    data: {
      tokens: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      },
    },
    message: 'Token refreshed successfully',
  };

  res.json(response);
}));

// Logout user
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (refreshToken) {
    // Remove refresh token from database
    await prisma.refreshToken.deleteMany({
      where: {
        token: refreshToken,
        userId: req.user!.id,
      },
    });
  }

  logInfo('User logged out successfully', { userId: req.user!.id });

  const response: ApiResponse = {
    success: true,
    message: 'Logout successful',
  };

  res.json(response);
}));

// Get user profile
router.get('/profile', authenticate, asyncHandler(async (req, res) => {
  const response: ApiResponse<{ user: User }> = {
    success: true,
    data: {
      user: req.user!,
    },
  };

  res.json(response);
}));

export default router;
