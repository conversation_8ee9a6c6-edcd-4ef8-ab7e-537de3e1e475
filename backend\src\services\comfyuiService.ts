import axios, { AxiosInstance } from 'axios';
import { config } from '@/config';
import { logExternalService, logError } from '@/utils/logger';
import { ComfyUIWorkflow, ComfyUIQueueItem } from '@shared/types';

export interface ComfyUIPrompt {
  [key: string]: {
    inputs: Record<string, any>;
    class_type: string;
    _meta?: {
      title?: string;
    };
  };
}

export interface ComfyUIQueueResponse {
  prompt_id: string;
  number: number;
  node_errors?: Record<string, any>;
}

export interface ComfyUIHistoryItem {
  prompt: ComfyUIPrompt;
  outputs: Record<string, any>;
  status: {
    status_str: string;
    completed: boolean;
    messages: string[];
  };
}

export class ComfyUIService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.externalServices.comfyui.baseUrl,
      timeout: config.externalServices.comfyui.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logExternalService('ComfyUI', `${config.method?.toUpperCase()} ${config.url}`, true);
        return config;
      },
      (error) => {
        logError('ComfyUI request error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logExternalService('ComfyUI', `${response.config.method?.toUpperCase()} ${response.config.url}`, true);
        return response;
      },
      (error) => {
        const method = error.config?.method?.toUpperCase() || 'UNKNOWN';
        const url = error.config?.url || 'unknown';
        logExternalService('ComfyUI', `${method} ${url}`, false, undefined, {
          status: error.response?.status,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  // Execute a workflow
  async executeWorkflow(prompt: ComfyUIPrompt, clientId?: string): Promise<ComfyUIQueueResponse> {
    try {
      const response = await this.client.post('/prompt', {
        prompt,
        client_id: clientId || 'ai-dev-stack',
      });
      return response.data;
    } catch (error) {
      logError('Failed to execute ComfyUI workflow', error);
      throw new Error('Failed to execute workflow');
    }
  }

  // Get queue status
  async getQueue(): Promise<{
    queue_running: any[];
    queue_pending: any[];
  }> {
    try {
      const response = await this.client.get('/queue');
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI queue', error);
      throw new Error('Failed to get queue status');
    }
  }

  // Get queue history
  async getHistory(maxItems: number = 100): Promise<Record<string, ComfyUIHistoryItem>> {
    try {
      const response = await this.client.get(`/history?max_items=${maxItems}`);
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI history', error);
      throw new Error('Failed to get execution history');
    }
  }

  // Cancel a queued item
  async cancelQueueItem(promptId: string): Promise<void> {
    try {
      await this.client.post('/queue', {
        delete: [promptId],
      });
    } catch (error) {
      logError(`Failed to cancel queue item ${promptId}`, error);
      throw new Error('Failed to cancel queue item');
    }
  }

  // Clear the queue
  async clearQueue(): Promise<void> {
    try {
      await this.client.post('/queue', {
        clear: true,
      });
    } catch (error) {
      logError('Failed to clear ComfyUI queue', error);
      throw new Error('Failed to clear queue');
    }
  }

  // Get system stats
  async getSystemStats(): Promise<any> {
    try {
      const response = await this.client.get('/system_stats');
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI system stats', error);
      throw new Error('Failed to get system stats');
    }
  }

  // Get available object info (nodes)
  async getObjectInfo(): Promise<Record<string, any>> {
    try {
      const response = await this.client.get('/object_info');
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI object info', error);
      throw new Error('Failed to get available nodes');
    }
  }

  // Upload an image
  async uploadImage(imageBuffer: Buffer, filename: string, subfolder?: string): Promise<{
    name: string;
    subfolder: string;
    type: string;
  }> {
    try {
      const formData = new FormData();
      const blob = new Blob([imageBuffer]);
      formData.append('image', blob, filename);
      if (subfolder) {
        formData.append('subfolder', subfolder);
      }

      const response = await this.client.post('/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      logError('Failed to upload image to ComfyUI', error);
      throw new Error('Failed to upload image');
    }
  }

  // Get an image
  async getImage(filename: string, subfolder?: string, type: string = 'output'): Promise<Buffer> {
    try {
      const params = new URLSearchParams({
        filename,
        type,
      });
      if (subfolder) {
        params.append('subfolder', subfolder);
      }

      const response = await this.client.get(`/view?${params.toString()}`, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data);
    } catch (error) {
      logError(`Failed to get image ${filename}`, error);
      throw new Error('Failed to get image');
    }
  }

  // Check if ComfyUI service is available
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/queue');
      return true;
    } catch (error) {
      return false;
    }
  }

  // Interrupt current execution
  async interrupt(): Promise<void> {
    try {
      await this.client.post('/interrupt');
    } catch (error) {
      logError('Failed to interrupt ComfyUI execution', error);
      throw new Error('Failed to interrupt execution');
    }
  }

  // Get embeddings
  async getEmbeddings(): Promise<any> {
    try {
      const response = await this.client.get('/embeddings');
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI embeddings', error);
      throw new Error('Failed to get embeddings');
    }
  }

  // Get extensions
  async getExtensions(): Promise<any> {
    try {
      const response = await this.client.get('/extensions');
      return response.data;
    } catch (error) {
      logError('Failed to get ComfyUI extensions', error);
      throw new Error('Failed to get extensions');
    }
  }

  // Convert workflow to API format
  convertWorkflowToAPI(workflow: ComfyUIWorkflow): ComfyUIPrompt {
    const prompt: ComfyUIPrompt = {};
    
    // Convert nodes to API format
    Object.entries(workflow.nodes).forEach(([nodeId, node]) => {
      prompt[nodeId] = {
        inputs: node.inputs || {},
        class_type: node.type,
        _meta: {
          title: node.properties?.title || node.type,
        },
      };
    });

    return prompt;
  }

  // Validate workflow
  async validateWorkflow(workflow: ComfyUIWorkflow): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    try {
      const prompt = this.convertWorkflowToAPI(workflow);
      
      // Try to queue the workflow with validation only
      const response = await this.client.post('/prompt', {
        prompt,
        validate_only: true,
      });

      return {
        valid: true,
        errors: [],
      };
    } catch (error: any) {
      const errors = [];
      if (error.response?.data?.node_errors) {
        Object.entries(error.response.data.node_errors).forEach(([nodeId, nodeError]: [string, any]) => {
          errors.push(`Node ${nodeId}: ${nodeError.message || 'Unknown error'}`);
        });
      } else {
        errors.push(error.message || 'Validation failed');
      }

      return {
        valid: false,
        errors,
      };
    }
  }
}
