import { z } from 'zod';

// Common validation schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Invalid email format');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');

export const usernameSchema = z
  .string()
  .min(3, 'Username must be at least 3 characters')
  .max(20, 'Username must be less than 20 characters')
  .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores');

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces');

// Authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

export const registerSchema = z.object({
  email: emailSchema,
  username: usernameSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  firstName: nameSchema,
  lastName: nameSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Profile schemas
export const updateProfileSchema = z.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  email: emailSchema.optional(),
  username: usernameSchema.optional(),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Chat schemas
export const chatMessageSchema = z.object({
  message: z.string().min(1, 'Message is required').max(4000, 'Message is too long'),
  model: z.string().min(1, 'Model is required'),
  conversationId: z.string().uuid().optional(),
  stream: z.boolean().optional(),
});

export const conversationSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title is too long'),
  model: z.string().min(1, 'Model is required'),
});

// Workflow schemas
export const workflowSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
  nodes: z.record(z.any()),
  connections: z.array(z.any()),
  version: z.string().optional(),
  isPublic: z.boolean().optional(),
});

export const executeWorkflowSchema = z.object({
  workflowId: z.string().uuid('Invalid workflow ID'),
  inputs: z.record(z.any()).optional(),
});

// Project schemas
export const projectSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
  path: z.string().min(1, 'Path is required'),
  language: z.string().min(1, 'Language is required'),
  framework: z.string().optional(),
});

export const fileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name is too long'),
  content: z.string(),
  language: z.string().min(1, 'Language is required'),
  path: z.string().optional(),
});

// Code analysis schemas
export const codeAnalysisSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  language: z.string().min(1, 'Language is required'),
  filePath: z.string().optional(),
});

export const codeSuggestionsSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  language: z.string().min(1, 'Language is required'),
  cursorPosition: z.object({
    line: z.number().min(0),
    column: z.number().min(0),
  }),
  context: z.string().optional(),
});

// System schemas
export const systemConfigSchema = z.object({
  ollamaUrl: z.string().url('Invalid Ollama URL').optional(),
  comfyuiUrl: z.string().url('Invalid ComfyUI URL').optional(),
  augmentCodeApiKey: z.string().optional(),
  enableMetrics: z.boolean().optional(),
  enableNotifications: z.boolean().optional(),
});

// File upload schemas
export const fileUploadSchema = z.object({
  file: z.any().refine((file) => file instanceof File, 'File is required'),
  type: z.enum(['image', 'code', 'workflow', 'other']),
  maxSize: z.number().optional(),
});

// Search and pagination schemas
export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  type: z.enum(['conversations', 'workflows', 'projects', 'files']).optional(),
  limit: z.number().min(1).max(100).optional(),
});

export const paginationSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  search: z.string().optional(),
});

// Validation helper functions
export function validateEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

export function validatePassword(password: string): boolean {
  return passwordSchema.safeParse(password).success;
}

export function validateUsername(username: string): boolean {
  return usernameSchema.safeParse(username).success;
}

export function getPasswordStrength(password: string): {
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Use at least 8 characters');
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add uppercase letters');
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add lowercase letters');
  }

  if (/[0-9]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add numbers');
  }

  if (/[^A-Za-z0-9]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Add special characters');
  }

  return { score, feedback };
}

export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
}

export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

export function validateFileSize(file: File, maxSizeInBytes: number): boolean {
  return file.size <= maxSizeInBytes;
}

export function formatValidationErrors(errors: z.ZodError): Record<string, string> {
  const formattedErrors: Record<string, string> = {};
  
  errors.errors.forEach((error) => {
    const path = error.path.join('.');
    formattedErrors[path] = error.message;
  });
  
  return formattedErrors;
}

// Custom validation rules
export const customValidations = {
  isValidUrl: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  isValidJson: (json: string) => {
    try {
      JSON.parse(json);
      return true;
    } catch {
      return false;
    }
  },

  isValidWorkflowNode: (node: any) => {
    return (
      typeof node === 'object' &&
      node !== null &&
      typeof node.type === 'string' &&
      typeof node.inputs === 'object' &&
      typeof node.outputs === 'object'
    );
  },

  isValidLanguage: (language: string) => {
    const supportedLanguages = [
      'javascript', 'typescript', 'python', 'java', 'cpp', 'c',
      'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'csharp',
      'html', 'css', 'scss', 'json', 'yaml', 'markdown', 'sql',
      'bash', 'plaintext'
    ];
    return supportedLanguages.includes(language.toLowerCase());
  },
};
