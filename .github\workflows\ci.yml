name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: ai_dev_stack_test
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ai_dev_stack_test
  JWT_SECRET: test-jwt-secret-for-ci
  REDIS_URL: redis://localhost:6379/1

jobs:
  # Lint and Type Check
  lint-and-types:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci
          cd ../shared && npm ci

      - name: Lint backend
        run: cd backend && npm run lint

      - name: Lint frontend
        run: cd frontend && npm run lint

      - name: Type check backend
        run: cd backend && npm run type-check

      - name: Type check frontend
        run: cd frontend && npm run type-check

  # Security Audit
  security-audit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci

      - name: Audit backend dependencies
        run: cd backend && npm audit --audit-level=moderate

      - name: Audit frontend dependencies
        run: cd frontend && npm audit --audit-level=moderate

  # Backend Tests
  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: cd backend && npm ci

      - name: Generate Prisma client
        run: cd backend && npx prisma generate

      - name: Run database migrations
        run: cd backend && npx prisma migrate deploy

      - name: Run unit tests
        run: cd backend && npm run test:coverage

      - name: Run integration tests
        run: cd backend && npm run test:integration

      - name: Upload backend coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Frontend Tests
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: cd frontend && npm ci

      - name: Run unit tests
        run: cd frontend && npm run test:coverage

      - name: Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci

      - name: Setup database
        run: |
          cd backend
          npx prisma generate
          npx prisma migrate deploy
          npm run db:seed

      - name: Build frontend
        run: cd frontend && npm run build

      - name: Install Playwright browsers
        run: cd frontend && npx playwright install --with-deps

      - name: Start backend server
        run: |
          cd backend
          npm run dev &
          echo $! > backend.pid
        env:
          NODE_ENV: test

      - name: Start frontend server
        run: |
          cd frontend
          npm run start &
          echo $! > frontend.pid

      - name: Wait for servers to start
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3001/health; do sleep 2; done'
          timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

      - name: Run E2E tests
        run: cd frontend && npx playwright test
        env:
          E2E_BASE_URL: http://localhost:3000

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: frontend/test-results/

      - name: Stop servers
        if: always()
        run: |
          if [ -f backend/backend.pid ]; then
            kill $(cat backend/backend.pid) || true
          fi
          if [ -f frontend/frontend.pid ]; then
            kill $(cat frontend/frontend.pid) || true
          fi

  # Build and Deploy (only on main branch)
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [lint-and-types, security-audit, backend-tests, frontend-tests, e2e-tests]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci

      - name: Build backend
        run: cd backend && npm run build

      - name: Build frontend
        run: cd frontend && npm run build

      - name: Build Docker images
        run: |
          docker build -t ai-dev-stack-backend ./backend
          docker build -t ai-dev-stack-frontend ./frontend

      - name: Run security scan on Docker images
        run: |
          # This would use tools like Trivy or Snyk to scan Docker images
          echo "Security scan would run here"

      - name: Deploy to staging
        run: |
          # This would deploy to staging environment
          echo "Deployment to staging would happen here"

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: cd frontend && npm ci

      - name: Build application
        run: cd frontend && npm run build

      - name: Analyze bundle size
        run: |
          cd frontend
          npm run analyze || echo "Bundle analysis completed"

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli
          # This would run Lighthouse against the deployed application
          echo "Lighthouse CI would run here"

  # Notification
  notify:
    runs-on: ubuntu-latest
    needs: [lint-and-types, security-audit, backend-tests, frontend-tests, e2e-tests]
    if: always()
    steps:
      - name: Notify on success
        if: ${{ needs.lint-and-types.result == 'success' && needs.security-audit.result == 'success' && needs.backend-tests.result == 'success' && needs.frontend-tests.result == 'success' && needs.e2e-tests.result == 'success' }}
        run: echo "All tests passed! ✅"

      - name: Notify on failure
        if: ${{ needs.lint-and-types.result == 'failure' || needs.security-audit.result == 'failure' || needs.backend-tests.result == 'failure' || needs.frontend-tests.result == 'failure' || needs.e2e-tests.result == 'failure' }}
        run: |
          echo "Some tests failed! ❌"
          echo "Lint and Types: ${{ needs.lint-and-types.result }}"
          echo "Security Audit: ${{ needs.security-audit.result }}"
          echo "Backend Tests: ${{ needs.backend-tests.result }}"
          echo "Frontend Tests: ${{ needs.frontend-tests.result }}"
          echo "E2E Tests: ${{ needs.e2e-tests.result }}"
