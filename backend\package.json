{"name": "@ai-dev-stack/backend", "version": "1.0.0", "description": "Backend API for AI Development Stack GUI", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "redis": "^4.6.11", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "uuid": "^9.0.1", "zod": "^3.22.4", "ws": "^8.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.0", "nodemon": "^3.0.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "engines": {"node": ">=18.0.0"}}