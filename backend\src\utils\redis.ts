import { createClient, RedisClientType } from 'redis';
import { config } from '@/config';
import { logger, logError } from '@/utils/logger';

// Global Redis client instance
let redisClient: RedisClientType;

// Create Redis client
function createRedisClient(): RedisClientType {
  const client = createClient({
    url: config.redis.url,
    socket: {
      reconnectStrategy: (retries) => {
        if (retries > 10) {
          logger.error('Redis: Too many reconnection attempts, giving up');
          return new Error('Too many reconnection attempts');
        }
        const delay = Math.min(retries * 100, 3000);
        logger.warn(`Redis: Reconnecting in ${delay}ms (attempt ${retries})`);
        return delay;
      },
    },
  });

  // Event listeners
  client.on('connect', () => {
    logger.info('Redis: Connecting...');
  });

  client.on('ready', () => {
    logger.info('✅ Redis: Connected and ready');
  });

  client.on('error', (error) => {
    logError('Redis: Connection error', error);
  });

  client.on('end', () => {
    logger.info('Redis: Connection ended');
  });

  client.on('reconnecting', () => {
    logger.info('Redis: Reconnecting...');
  });

  return client;
}

// Get Redis client instance (singleton)
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    redisClient = createRedisClient();
  }
  return redisClient;
}

// Connect to Redis
export async function connectRedis(): Promise<void> {
  try {
    const startTime = Date.now();
    
    if (!redisClient) {
      redisClient = createRedisClient();
    }

    if (!redisClient.isOpen) {
      await redisClient.connect();
    }

    // Test Redis connection
    await redisClient.ping();
    
    const duration = Date.now() - startTime;
    logger.info('✅ Redis connected successfully', { duration: `${duration}ms` });
    
  } catch (error) {
    logError('❌ Failed to connect to Redis', error);
    throw error;
  }
}

// Disconnect from Redis
export async function disconnectRedis(): Promise<void> {
  try {
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
      logger.info('Redis disconnected');
    }
  } catch (error) {
    logError('Error disconnecting from Redis', error);
    throw error;
  }
}

// Redis health check
export async function checkRedisHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    await redisClient.ping();
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      status: 'unhealthy',
      responseTime,
      error: errorMessage,
    };
  }
}

// Cache helper functions
export class CacheService {
  private client: RedisClientType;

  constructor() {
    this.client = getRedisClient();
  }

  // Set cache with TTL
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      
      if (ttlSeconds) {
        await this.client.setEx(key, ttlSeconds, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      logError(`Cache: Failed to set key ${key}`, error);
      throw error;
    }
  }

  // Get cache
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logError(`Cache: Failed to get key ${key}`, error);
      return null;
    }
  }

  // Delete cache
  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      logError(`Cache: Failed to delete key ${key}`, error);
      throw error;
    }
  }

  // Check if key exists
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logError(`Cache: Failed to check existence of key ${key}`, error);
      return false;
    }
  }

  // Set TTL for existing key
  async expire(key: string, ttlSeconds: number): Promise<void> {
    try {
      await this.client.expire(key, ttlSeconds);
    } catch (error) {
      logError(`Cache: Failed to set TTL for key ${key}`, error);
      throw error;
    }
  }

  // Get TTL for key
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      logError(`Cache: Failed to get TTL for key ${key}`, error);
      return -1;
    }
  }

  // Increment counter
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      logError(`Cache: Failed to increment key ${key}`, error);
      throw error;
    }
  }

  // Set with pattern matching
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      logError(`Cache: Failed to get keys with pattern ${pattern}`, error);
      return [];
    }
  }

  // Clear all cache
  async flushAll(): Promise<void> {
    try {
      await this.client.flushAll();
    } catch (error) {
      logError('Cache: Failed to flush all keys', error);
      throw error;
    }
  }

  // Hash operations
  async hSet(key: string, field: string, value: any): Promise<void> {
    try {
      await this.client.hSet(key, field, JSON.stringify(value));
    } catch (error) {
      logError(`Cache: Failed to set hash field ${field} in key ${key}`, error);
      throw error;
    }
  }

  async hGet<T>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.client.hGet(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logError(`Cache: Failed to get hash field ${field} from key ${key}`, error);
      return null;
    }
  }

  async hGetAll<T>(key: string): Promise<Record<string, T>> {
    try {
      const hash = await this.client.hGetAll(key);
      const result: Record<string, T> = {};
      
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value);
      }
      
      return result;
    } catch (error) {
      logError(`Cache: Failed to get all hash fields from key ${key}`, error);
      return {};
    }
  }

  async hDel(key: string, field: string): Promise<void> {
    try {
      await this.client.hDel(key, field);
    } catch (error) {
      logError(`Cache: Failed to delete hash field ${field} from key ${key}`, error);
      throw error;
    }
  }
}

// Session management
export class SessionService {
  private cache: CacheService;
  private readonly SESSION_PREFIX = 'session:';
  private readonly SESSION_TTL = 7 * 24 * 60 * 60; // 7 days

  constructor() {
    this.cache = new CacheService();
  }

  async createSession(sessionId: string, data: any): Promise<void> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    await this.cache.set(key, data, this.SESSION_TTL);
  }

  async getSession<T>(sessionId: string): Promise<T | null> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    return await this.cache.get<T>(key);
  }

  async updateSession(sessionId: string, data: any): Promise<void> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    await this.cache.set(key, data, this.SESSION_TTL);
  }

  async deleteSession(sessionId: string): Promise<void> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    await this.cache.del(key);
  }

  async refreshSession(sessionId: string): Promise<void> {
    const key = `${this.SESSION_PREFIX}${sessionId}`;
    await this.cache.expire(key, this.SESSION_TTL);
  }
}

// Export instances
export const cacheService = new CacheService();
export const sessionService = new SessionService();
export { redisClient };
export default getRedisClient;
