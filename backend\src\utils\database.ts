import { PrismaClient } from '@prisma/client';
import { config } from '@/config';
import { logger, logDatabase, logError } from '@/utils/logger';

// Global Prisma instance
let prisma: PrismaClient;

// Prisma client configuration
const prismaConfig = {
  datasources: {
    db: {
      url: config.database.url,
    },
  },
  log: [
    { level: 'query', emit: 'event' },
    { level: 'error', emit: 'event' },
    { level: 'info', emit: 'event' },
    { level: 'warn', emit: 'event' },
  ] as const,
};

// Create Prisma client instance
function createPrismaClient(): PrismaClient {
  const client = new PrismaClient(prismaConfig);

  // Log database queries in development
  if (config.isDevelopment) {
    client.$on('query', (e) => {
      logDatabase('Query', 'unknown', e.duration, {
        query: e.query,
        params: e.params,
      });
    });
  }

  // Log database errors
  client.$on('error', (e) => {
    logError('Database error', e);
  });

  // Log database info
  client.$on('info', (e) => {
    logger.info(`Database: ${e.message}`);
  });

  // Log database warnings
  client.$on('warn', (e) => {
    logger.warn(`Database: ${e.message}`);
  });

  return client;
}

// Get Prisma client instance (singleton)
export function getPrismaClient(): PrismaClient {
  if (!prisma) {
    prisma = createPrismaClient();
  }
  return prisma;
}

// Connect to database
export async function connectDatabase(): Promise<void> {
  try {
    const startTime = Date.now();
    
    if (!prisma) {
      prisma = createPrismaClient();
    }

    // Test database connection
    await prisma.$connect();
    
    // Run a simple query to verify connection
    await prisma.$queryRaw`SELECT 1`;
    
    const duration = Date.now() - startTime;
    logger.info('✅ Database connected successfully', { duration: `${duration}ms` });
    
  } catch (error) {
    logError('❌ Failed to connect to database', error);
    throw error;
  }
}

// Disconnect from database
export async function disconnectDatabase(): Promise<void> {
  try {
    if (prisma) {
      await prisma.$disconnect();
      logger.info('Database disconnected');
    }
  } catch (error) {
    logError('Error disconnecting from database', error);
    throw error;
  }
}

// Database health check
export async function checkDatabaseHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      status: 'unhealthy',
      responseTime,
      error: errorMessage,
    };
  }
}

// Transaction helper
export async function withTransaction<T>(
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await callback(tx);
    });
    
    const duration = Date.now() - startTime;
    logDatabase('Transaction', 'completed', duration);
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    logDatabase('Transaction', 'failed', duration);
    logError('Transaction failed', error);
    throw error;
  }
}

// Pagination helper
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export function createPaginationQuery(options: PaginationOptions) {
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(100, Math.max(1, options.limit || 20));
  const skip = (page - 1) * limit;

  const orderBy = options.sortBy ? {
    [options.sortBy]: options.sortOrder || 'desc'
  } : undefined;

  return {
    skip,
    take: limit,
    orderBy,
    page,
    limit,
  };
}

export function createPaginationResult<T>(
  items: T[],
  total: number,
  page: number,
  limit: number
): PaginationResult<T> {
  const totalPages = Math.ceil(total / limit);
  
  return {
    items,
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

// Soft delete helper
export async function softDelete(
  model: any,
  id: string,
  deletedBy?: string
): Promise<void> {
  await model.update({
    where: { id },
    data: {
      deletedAt: new Date(),
      deletedBy,
    },
  });
}

// Bulk operations helper
export async function bulkCreate<T>(
  model: any,
  data: T[],
  batchSize: number = 100
): Promise<void> {
  const batches = [];
  for (let i = 0; i < data.length; i += batchSize) {
    batches.push(data.slice(i, i + batchSize));
  }

  for (const batch of batches) {
    await model.createMany({
      data: batch,
      skipDuplicates: true,
    });
  }
}

// Export the Prisma client instance
export { prisma };
export default getPrismaClient;
