'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  Server, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { formatBytes, formatRelativeTime } from '@/lib/utils';
import { systemApi } from '@/lib/api';
import { useWebSocket } from '@/hooks/useWebSocket';
import { WS_EVENTS } from '@shared/utils/constants';

export default function SystemPage() {
  const [metrics, setMetrics] = useState<any>(null);
  const [services, setServices] = useState<any[]>([]);
  const { addEventListener } = useWebSocket();

  // Fetch system status
  const { data: statusData, isLoading, refetch } = useQuery({
    queryKey: ['system', 'status'],
    queryFn: systemApi.getStatus,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch system metrics
  const { data: metricsData } = useQuery({
    queryKey: ['system', 'metrics'],
    queryFn: systemApi.getMetrics,
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  // Listen for real-time updates via WebSocket
  useEffect(() => {
    const unsubscribeMetrics = addEventListener(WS_EVENTS.METRICS_UPDATE, (data) => {
      setMetrics(data.metrics);
    });

    const unsubscribeStatus = addEventListener(WS_EVENTS.SYSTEM_STATUS, (data) => {
      setServices(data.services);
    });

    return () => {
      unsubscribeMetrics();
      unsubscribeStatus();
    };
  }, [addEventListener]);

  // Initialize data from API
  useEffect(() => {
    if (statusData?.data?.services) {
      setServices(statusData.data.services);
    }
  }, [statusData]);

  useEffect(() => {
    if (metricsData?.data?.metrics) {
      setMetrics(metricsData.data.metrics);
    }
  }, [metricsData]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'default';
      case 'offline':
        return 'destructive';
      case 'error':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getProgressColor = (value: number) => {
    if (value < 50) return 'bg-green-500';
    if (value < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Monitor</h1>
          <p className="text-muted-foreground">
            Monitor system performance and service health
          </p>
        </div>
        <Button onClick={() => refetch()} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Metrics */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
              <Cpu className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.cpu.toFixed(1)}%</div>
              <Progress 
                value={metrics.cpu} 
                className="mt-2"
                indicatorClassName={getProgressColor(metrics.cpu)}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.memory.toFixed(1)}%</div>
              <Progress 
                value={metrics.memory} 
                className="mt-2"
                indicatorClassName={getProgressColor(metrics.memory)}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.disk.toFixed(1)}%</div>
              <Progress 
                value={metrics.disk} 
                className="mt-2"
                indicatorClassName={getProgressColor(metrics.disk)}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Network</CardTitle>
              <Wifi className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="text-sm">
                  ↑ {formatBytes(metrics.network.upload)}/s
                </div>
                <div className="text-sm">
                  ↓ {formatBytes(metrics.network.download)}/s
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle>Service Status</CardTitle>
          <CardDescription>
            Current status of all integrated services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {services.map((service) => (
              <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(service.status)}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{service.name}</span>
                      <Badge variant={getStatusColor(service.status) as any}>
                        {service.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{service.url}</p>
                    {service.error && (
                      <p className="text-sm text-red-500">{service.error}</p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  {service.responseTime && (
                    <div className="text-sm text-muted-foreground">
                      {service.responseTime}ms
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    {formatRelativeTime(service.lastCheck)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
            <CardDescription>
              Basic system and runtime information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Platform:</span>
              <span>{typeof window !== 'undefined' ? navigator.platform : 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">User Agent:</span>
              <span className="text-right text-sm">
                {typeof window !== 'undefined' ? navigator.userAgent.split(' ')[0] : 'Unknown'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Language:</span>
              <span>{typeof window !== 'undefined' ? navigator.language : 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Online:</span>
              <span>{typeof window !== 'undefined' ? (navigator.onLine ? 'Yes' : 'No') : 'Unknown'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Application Status</CardTitle>
            <CardDescription>
              Current application state and configuration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Version:</span>
              <span>1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Environment:</span>
              <span>{process.env.NODE_ENV || 'development'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">API URL:</span>
              <span className="text-right text-sm">
                {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">WebSocket:</span>
              <span className="text-right text-sm">
                {process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
