import { Server as SocketIOServer, Socket } from 'socket.io';
import { authenticateSocket } from '@/middleware/auth';
import { logWebSocket, logSecurity } from '@/utils/logger';
import { WS_EVENTS } from '@shared/utils/constants';
import { User } from '@shared/types';

// Extend Socket interface to include user
interface AuthenticatedSocket extends Socket {
  user?: User;
}

// Connected users map
const connectedUsers = new Map<string, AuthenticatedSocket>();

export function setupWebSocket(io: SocketIOServer): void {
  // Authentication middleware for WebSocket
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        logSecurity('WebSocket connection rejected: No token', undefined, socket.handshake.address);
        return next(new Error('Authentication required'));
      }

      const user = await authenticateSocket(token);
      
      if (!user) {
        logSecurity('WebSocket connection rejected: Invalid token', undefined, socket.handshake.address);
        return next(new Error('Invalid token'));
      }

      socket.user = user;
      next();
    } catch (error) {
      logSecurity('WebSocket authentication error', undefined, socket.handshake.address, { error: error.message });
      next(new Error('Authentication failed'));
    }
  });

  // Handle connections
  io.on('connection', (socket: AuthenticatedSocket) => {
    if (!socket.user) {
      socket.disconnect();
      return;
    }

    const userId = socket.user.id;
    connectedUsers.set(userId, socket);

    logWebSocket('User connected', socket.id, {
      userId,
      userEmail: socket.user.email,
      totalConnections: connectedUsers.size,
    });

    // Send welcome message
    socket.emit(WS_EVENTS.AUTHENTICATED, {
      user: socket.user,
      timestamp: new Date(),
    });

    // Handle chat messages
    socket.on(WS_EVENTS.CHAT_MESSAGE, async (data) => {
      try {
        logWebSocket('Chat message received', socket.id, { userId, data });

        const { message, model, conversationId, stream = true } = data;

        if (!message || !model) {
          socket.emit(WS_EVENTS.CHAT_ERROR, {
            error: 'Message and model are required',
            timestamp: new Date(),
          });
          return;
        }

        // Import services dynamically to avoid circular dependencies
        const { OllamaService } = await import('@/services/ollamaService');
        const { getPrismaClient } = await import('@/utils/database');

        const ollamaService = new OllamaService();
        const prisma = getPrismaClient();

        // Create or get conversation
        let conversation;
        if (conversationId) {
          conversation = await prisma.conversation.findFirst({
            where: { id: conversationId, userId },
          });
        }

        if (!conversation) {
          conversation = await prisma.conversation.create({
            data: {
              title: message.slice(0, 50) + '...',
              model,
              userId,
            },
          });
        }

        // Save user message
        await prisma.message.create({
          data: {
            role: 'user',
            content: message,
            conversationId: conversation.id,
          },
        });

        // Get AI response
        const response = await ollamaService.chat({
          model,
          messages: [{ role: 'user', content: message }],
          stream: false,
        });

        // Save assistant message
        await prisma.message.create({
          data: {
            role: 'assistant',
            content: response.message.content,
            conversationId: conversation.id,
          },
        });

        // Send response
        socket.emit(WS_EVENTS.CHAT_STREAM, {
          conversationId: conversation.id,
          content: response.message.content,
          done: true,
          timestamp: new Date(),
        });
      } catch (error) {
        logWebSocket('Chat message error', socket.id, { userId, error: error.message });
        socket.emit(WS_EVENTS.CHAT_ERROR, {
          error: 'Failed to process message',
          timestamp: new Date(),
        });
      }
    });

    // Handle workflow execution requests
    socket.on(WS_EVENTS.WORKFLOW_EXECUTE, async (data) => {
      try {
        logWebSocket('Workflow execution requested', socket.id, { userId, workflowId: data.workflowId });

        const { workflowId, inputs = {} } = data;

        if (!workflowId) {
          socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
            error: 'Workflow ID is required',
            timestamp: new Date(),
          });
          return;
        }

        // Import services dynamically
        const { ComfyUIService } = await import('@/services/comfyuiService');
        const { getPrismaClient } = await import('@/utils/database');

        const comfyuiService = new ComfyUIService();
        const prisma = getPrismaClient();

        // Get workflow
        const workflow = await prisma.workflow.findFirst({
          where: { id: workflowId, userId },
        });

        if (!workflow) {
          socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
            error: 'Workflow not found',
            timestamp: new Date(),
          });
          return;
        }

        // Convert workflow to ComfyUI format
        const prompt = comfyuiService.convertWorkflowToAPI(workflow as any);

        // Execute workflow
        const execution = await comfyuiService.executeWorkflow(prompt);

        // Create queue item
        const queueItem = await prisma.queueItem.create({
          data: {
            workflowId: workflow.id,
            status: 'PENDING',
            userId,
            results: { prompt_id: execution.prompt_id },
          },
        });

        // Emit execution started
        socket.emit(WS_EVENTS.WORKFLOW_STARTED, {
          workflowId: workflow.id,
          queueItemId: queueItem.id,
          promptId: execution.prompt_id,
          timestamp: new Date(),
        });

        // Simulate progress updates (in real implementation, this would come from ComfyUI webhooks)
        setTimeout(() => {
          socket.emit(WS_EVENTS.WORKFLOW_PROGRESS, {
            workflowId: data.workflowId,
            progress: 50,
            status: 'processing',
            timestamp: new Date(),
          });
        }, 2000);

        setTimeout(() => {
          socket.emit(WS_EVENTS.GENERATION_COMPLETE, {
            workflowId: data.workflowId,
            progress: 100,
            status: 'completed',
            results: ['generated_image.png'],
            timestamp: new Date(),
          });
        }, 5000);
      } catch (error) {
        logWebSocket('Workflow execution error', socket.id, { userId, error: error.message });
        socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
          workflowId: data.workflowId,
          error: 'Failed to execute workflow',
          timestamp: new Date(),
        });
      }
    });

    // Handle code analysis requests
    socket.on(WS_EVENTS.CODE_ANALYZE, async (data) => {
      try {
        logWebSocket('Code analysis requested', socket.id, { userId, language: data.language });

        const { code, language, filePath } = data;

        if (!code || !language) {
          socket.emit(WS_EVENTS.CODE_ERROR, {
            error: 'Code and language are required',
            timestamp: new Date(),
          });
          return;
        }

        // Import service dynamically
        const { AugmentCodeService } = await import('@/services/augmentCodeService');
        const augmentCodeService = new AugmentCodeService();

        // Analyze code
        const analysis = await augmentCodeService.analyzeCode({
          code,
          language,
          filePath,
        });

        socket.emit(WS_EVENTS.CODE_ANALYSIS_RESULT, {
          analysis,
          timestamp: new Date(),
        });
      } catch (error) {
        logWebSocket('Code analysis error', socket.id, { userId, error: error.message });
        socket.emit(WS_EVENTS.CODE_ERROR, {
          error: 'Failed to analyze code',
          timestamp: new Date(),
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      connectedUsers.delete(userId);
      
      logWebSocket('User disconnected', socket.id, {
        userId,
        reason,
        totalConnections: connectedUsers.size,
      });
    });

    // Handle errors
    socket.on('error', (error) => {
      logWebSocket('Socket error', socket.id, { userId, error: error.message });
    });
  });

  // Handle server errors
  io.on('error', (error) => {
    logWebSocket('Server error', 'server', { error: error.message });
  });
}

// Broadcast message to all connected users
export function broadcastToAll(event: string, data: any): void {
  for (const socket of connectedUsers.values()) {
    socket.emit(event, data);
  }
}

// Broadcast message to specific user
export function broadcastToUser(userId: string, event: string, data: any): void {
  const socket = connectedUsers.get(userId);
  if (socket) {
    socket.emit(event, data);
  }
}

// Broadcast message to multiple users
export function broadcastToUsers(userIds: string[], event: string, data: any): void {
  for (const userId of userIds) {
    broadcastToUser(userId, event, data);
  }
}

// Get connected users count
export function getConnectedUsersCount(): number {
  return connectedUsers.size;
}

// Get connected user IDs
export function getConnectedUserIds(): string[] {
  return Array.from(connectedUsers.keys());
}

// Check if user is connected
export function isUserConnected(userId: string): boolean {
  return connectedUsers.has(userId);
}

// Disconnect user
export function disconnectUser(userId: string, reason?: string): void {
  const socket = connectedUsers.get(userId);
  if (socket) {
    socket.disconnect(true);
    logWebSocket('User forcibly disconnected', socket.id, { userId, reason });
  }
}
