import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { externalApiRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, NotFoundError, ExternalServiceError } from '@/middleware/errorHandler';
import { getPrismaClient, createPaginationQuery, createPaginationResult } from '@/utils/database';
import { comfyUIValidation, paginationValidation } from '@shared/utils/validation';
import { logExternalService } from '@/utils/logger';
import { ComfyUIService } from '@/services/comfyuiService';
import { ApiResponse, ComfyUIWorkflow, ComfyUIQueueItem } from '@shared/types';

const router = Router();
const prisma = getPrismaClient();
const comfyuiService = new ComfyUIService();

// Apply authentication and rate limiting to all routes
router.use(authenticate);
router.use(externalApiRateLimiter);

// List user workflows
router.get('/workflows', asyncHandler(async (req, res) => {
  const query = paginationValidation.parse(req.query);
  const userId = req.user!.id;
  const { skip, take, orderBy, page, limit } = createPaginationQuery(query);

  const [workflows, total] = await Promise.all([
    prisma.workflow.findMany({
      where: { userId },
      skip,
      take,
      orderBy: orderBy || { updatedAt: 'desc' },
    }),
    prisma.workflow.count({ where: { userId } }),
  ]);

  const paginatedResult = createPaginationResult(workflows, total, page, limit);

  const response: ApiResponse<typeof paginatedResult> = {
    success: true,
    data: paginatedResult,
  };

  res.json(response);
}));

// Create new workflow
router.post('/workflows', asyncHandler(async (req, res) => {
  const validatedData = comfyUIValidation.createWorkflow.parse(req.body);
  const userId = req.user!.id;

  // Validate workflow with ComfyUI
  const validation = await comfyuiService.validateWorkflow(validatedData as any);
  if (!validation.valid) {
    throw new ExternalServiceError('ComfyUI', `Workflow validation failed: ${validation.errors.join(', ')}`);
  }

  const workflow = await prisma.workflow.create({
    data: {
      ...validatedData,
      userId,
    },
  });

  const response: ApiResponse<{ workflow: any }> = {
    success: true,
    data: { workflow },
    message: 'Workflow created successfully',
  };

  res.json(response);
}));

// Get specific workflow
router.get('/workflows/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const workflow = await prisma.workflow.findFirst({
    where: { id, userId },
  });

  if (!workflow) {
    throw new NotFoundError('Workflow not found');
  }

  const response: ApiResponse<{ workflow: any }> = {
    success: true,
    data: { workflow },
  };

  res.json(response);
}));

// Update workflow
router.put('/workflows/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const validatedData = comfyUIValidation.updateWorkflow.parse(req.body);

  const workflow = await prisma.workflow.findFirst({
    where: { id, userId },
  });

  if (!workflow) {
    throw new NotFoundError('Workflow not found');
  }

  // Validate workflow if nodes are being updated
  if (validatedData.nodes) {
    const validation = await comfyuiService.validateWorkflow({
      ...workflow,
      ...validatedData,
    } as any);
    if (!validation.valid) {
      throw new ExternalServiceError('ComfyUI', `Workflow validation failed: ${validation.errors.join(', ')}`);
    }
  }

  const updatedWorkflow = await prisma.workflow.update({
    where: { id },
    data: validatedData,
  });

  const response: ApiResponse<{ workflow: any }> = {
    success: true,
    data: { workflow: updatedWorkflow },
    message: 'Workflow updated successfully',
  };

  res.json(response);
}));

// Delete workflow
router.delete('/workflows/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const workflow = await prisma.workflow.findFirst({
    where: { id, userId },
  });

  if (!workflow) {
    throw new NotFoundError('Workflow not found');
  }

  await prisma.workflow.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Workflow deleted successfully',
  };

  res.json(response);
}));

// Execute workflow
router.post('/execute', asyncHandler(async (req, res) => {
  const validatedData = comfyUIValidation.executeWorkflow.parse(req.body);
  const userId = req.user!.id;

  // Get workflow
  const workflow = await prisma.workflow.findFirst({
    where: { id: validatedData.workflowId, userId },
  });

  if (!workflow) {
    throw new NotFoundError('Workflow not found');
  }

  try {
    // Convert workflow to ComfyUI API format
    const prompt = comfyuiService.convertWorkflowToAPI(workflow as any);

    // Execute workflow
    const execution = await comfyuiService.executeWorkflow(prompt);

    // Create queue item in database
    const queueItem = await prisma.queueItem.create({
      data: {
        workflowId: workflow.id,
        status: 'PENDING',
        userId,
        // Store the prompt_id from ComfyUI for tracking
        results: { prompt_id: execution.prompt_id },
      },
    });

    const response: ApiResponse<{ queueItem: any; execution: any }> = {
      success: true,
      data: { queueItem, execution },
      message: 'Workflow execution started',
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('ComfyUI', 'Failed to execute workflow');
  }
}));

// Get queue status
router.get('/queue', asyncHandler(async (req, res) => {
  const userId = req.user!.id;

  try {
    // Get ComfyUI queue status
    const comfyQueue = await comfyuiService.getQueue();

    // Get user's queue items from database
    const userQueueItems = await prisma.queueItem.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 50,
      include: {
        workflow: {
          select: { name: true },
        },
      },
    });

    const response: ApiResponse<{
      comfyQueue: any;
      userQueueItems: any[]
    }> = {
      success: true,
      data: {
        comfyQueue,
        userQueueItems,
      },
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('ComfyUI', 'Failed to get queue status');
  }
}));

// Get queue item
router.get('/queue/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const queueItem = await prisma.queueItem.findFirst({
    where: { id, userId },
    include: {
      workflow: true,
    },
  });

  if (!queueItem) {
    throw new NotFoundError('Queue item not found');
  }

  const response: ApiResponse<{ queueItem: any }> = {
    success: true,
    data: { queueItem },
  };

  res.json(response);
}));

// Cancel queue item
router.delete('/queue/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const queueItem = await prisma.queueItem.findFirst({
    where: { id, userId },
  });

  if (!queueItem) {
    throw new NotFoundError('Queue item not found');
  }

  try {
    // Cancel in ComfyUI if we have a prompt_id
    const promptId = queueItem.results?.prompt_id;
    if (promptId) {
      await comfyuiService.cancelQueueItem(promptId);
    }

    // Update status in database
    await prisma.queueItem.update({
      where: { id },
      data: { status: 'CANCELLED' },
    });

    const response: ApiResponse = {
      success: true,
      message: 'Queue item cancelled successfully',
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('ComfyUI', 'Failed to cancel queue item');
  }
}));

// Get available nodes
router.get('/nodes', asyncHandler(async (req, res) => {
  try {
    const nodes = await comfyuiService.getObjectInfo();

    const response: ApiResponse<{ nodes: any }> = {
      success: true,
      data: { nodes },
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('ComfyUI', 'Failed to get available nodes');
  }
}));

// Get execution history
router.get('/history', asyncHandler(async (req, res) => {
  const query = paginationValidation.parse(req.query);
  const userId = req.user!.id;
  const { skip, take, page, limit } = createPaginationQuery(query);

  try {
    // Get ComfyUI history
    const comfyHistory = await comfyuiService.getHistory(100);

    // Get user's completed queue items
    const [userHistory, total] = await Promise.all([
      prisma.queueItem.findMany({
        where: {
          userId,
          status: { in: ['COMPLETED', 'FAILED'] },
        },
        skip,
        take,
        orderBy: { completedAt: 'desc' },
        include: {
          workflow: {
            select: { name: true },
          },
        },
      }),
      prisma.queueItem.count({
        where: {
          userId,
          status: { in: ['COMPLETED', 'FAILED'] },
        },
      }),
    ]);

    const paginatedResult = createPaginationResult(userHistory, total, page, limit);

    const response: ApiResponse<typeof paginatedResult & { comfyHistory: any }> = {
      success: true,
      data: {
        ...paginatedResult,
        comfyHistory,
      },
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('ComfyUI', 'Failed to get execution history');
  }
}));

export default router;
