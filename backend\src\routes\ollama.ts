import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { externalApiRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, NotFoundError, ExternalServiceError } from '@/middleware/errorHandler';
import { getPrismaClient, createPaginationQuery, createPaginationResult } from '@/utils/database';
import { ollamaValidation, chatValidation, paginationValidation } from '@shared/utils/validation';
import { logExternalService } from '@/utils/logger';
import { OllamaService } from '@/services/ollamaService';
import { ApiResponse, OllamaModel, Conversation, ChatMessage } from '@shared/types';

const router = Router();
const prisma = getPrismaClient();
const ollamaService = new OllamaService();

// Apply authentication and rate limiting to all routes
router.use(authenticate);
router.use(externalApiRateLimiter);

// List available models
router.get('/models', asyncHandler(async (req, res) => {
  const startTime = Date.now();

  try {
    const models = await ollamaService.listModels();

    logExternalService('Ollama', 'list_models', true, Date.now() - startTime);

    const response: ApiResponse<{ models: OllamaModel[] }> = {
      success: true,
      data: { models },
    };

    res.json(response);
  } catch (error) {
    logExternalService('Ollama', 'list_models', false, Date.now() - startTime, { error: error.message });
    throw new ExternalServiceError('Ollama', 'Failed to fetch models');
  }
}));

// Get model information
router.get('/models/:name', asyncHandler(async (req, res) => {
  const { name } = req.params;
  const validatedData = ollamaValidation.modelInfo.parse({ name });
  const startTime = Date.now();

  try {
    const modelInfo = await ollamaService.getModelInfo(validatedData.name);

    logExternalService('Ollama', 'model_info', true, Date.now() - startTime);

    const response: ApiResponse<{ model: any }> = {
      success: true,
      data: { model: modelInfo },
    };

    res.json(response);
  } catch (error) {
    logExternalService('Ollama', 'model_info', false, Date.now() - startTime, { error: error.message });
    throw new ExternalServiceError('Ollama', 'Failed to fetch model information');
  }
}));

// Pull/download a model
router.post('/pull', asyncHandler(async (req, res) => {
  const validatedData = ollamaValidation.pullModel.parse(req.body);
  const startTime = Date.now();

  try {
    await ollamaService.pullModel(validatedData.name, validatedData.stream);

    logExternalService('Ollama', 'pull_model', true, Date.now() - startTime);

    const response: ApiResponse = {
      success: true,
      message: `Model ${validatedData.name} pull initiated`,
    };

    res.json(response);
  } catch (error) {
    logExternalService('Ollama', 'pull_model', false, Date.now() - startTime, { error: error.message });
    throw new ExternalServiceError('Ollama', 'Failed to pull model');
  }
}));

// Delete a model
router.delete('/models/:name', asyncHandler(async (req, res) => {
  const { name } = req.params;
  const validatedData = ollamaValidation.deleteModel.parse({ name });
  const startTime = Date.now();

  try {
    await ollamaService.deleteModel(validatedData.name);

    logExternalService('Ollama', 'delete_model', true, Date.now() - startTime);

    const response: ApiResponse = {
      success: true,
      message: `Model ${validatedData.name} deleted successfully`,
    };

    res.json(response);
  } catch (error) {
    logExternalService('Ollama', 'delete_model', false, Date.now() - startTime, { error: error.message });
    throw new ExternalServiceError('Ollama', 'Failed to delete model');
  }
}));

// Send chat message
router.post('/chat', asyncHandler(async (req, res) => {
  const validatedData = chatValidation.sendMessage.parse(req.body);
  const userId = req.user!.id;

  try {
    // Create or get conversation
    let conversation;
    if (validatedData.conversationId) {
      conversation = await prisma.conversation.findFirst({
        where: { id: validatedData.conversationId, userId },
      });
      if (!conversation) {
        throw new NotFoundError('Conversation not found');
      }
    } else {
      conversation = await prisma.conversation.create({
        data: {
          title: validatedData.message.slice(0, 50) + '...',
          model: validatedData.model,
          userId,
        },
      });
    }

    // Save user message
    await prisma.message.create({
      data: {
        role: 'user',
        content: validatedData.message,
        conversationId: conversation.id,
      },
    });

    // Send to Ollama and get response
    const response = await ollamaService.chat({
      model: validatedData.model,
      messages: [{ role: 'user', content: validatedData.message }],
      stream: validatedData.stream || false,
    });

    // Save assistant message
    await prisma.message.create({
      data: {
        role: 'assistant',
        content: response.message.content,
        conversationId: conversation.id,
      },
    });

    const apiResponse: ApiResponse<{ conversation: any; response: any }> = {
      success: true,
      data: { conversation, response },
    };

    res.json(apiResponse);
  } catch (error) {
    throw new ExternalServiceError('Ollama', 'Failed to process chat message');
  }
}));

export default router;
