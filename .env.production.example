# Production Environment Configuration
# Copy this file to .env.production and update the values

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password_here
DATABASE_URL=**********************************************************************/ai_dev_stack

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:your_secure_redis_password_here@redis:6379

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_at_least_64_characters_long_for_production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# External Services
OLLAMA_BASE_URL=http://your-ollama-server:11434
COMFYUI_BASE_URL=http://your-comfyui-server:8188
AUGMENT_CODE_API_KEY=your_augment_code_api_key_here

# Application URLs
NEXT_PUBLIC_API_URL=https://your-domain.com/api
NEXT_PUBLIC_WS_URL=wss://your-domain.com
CORS_ORIGIN=https://your-domain.com

# Logging
LOG_LEVEL=info

# File Upload
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=50MB

# Monitoring (optional)
GRAFANA_PASSWORD=your_secure_grafana_password_here

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Email Configuration (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
FROM_EMAIL=<EMAIL>

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX_REQUESTS=5

# Performance
NODE_OPTIONS=--max-old-space-size=2048
UV_THREADPOOL_SIZE=128

# Feature Flags
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true
ENABLE_COMPRESSION=true
