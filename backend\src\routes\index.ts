import { Express } from 'express';
import authRoutes from './auth';
import ollamaRoutes from './ollama';
import comfyuiRoutes from './comfyui';
import augmentRoutes from './augment';
import systemRoutes from './system';
import userRoutes from './user';

export function setupRoutes(app: Express): void {
  // API prefix
  const apiPrefix = '/api';

  // Health check (no prefix)
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  });

  // Authentication routes
  app.use(`${apiPrefix}/auth`, authRoutes);

  // User routes
  app.use(`${apiPrefix}/users`, userRoutes);

  // Ollama routes
  app.use(`${apiPrefix}/ollama`, ollamaRoutes);

  // ComfyUI routes
  app.use(`${apiPrefix}/comfyui`, comfyuiRoutes);

  // Augment Code routes
  app.use(`${apiPrefix}/augment`, augmentRoutes);

  // System routes
  app.use(`${apiPrefix}/system`, systemRoutes);

  // API documentation (in development)
  if (process.env.NODE_ENV === 'development') {
    app.get(`${apiPrefix}/docs`, (req, res) => {
      res.json({
        message: 'API Documentation',
        version: '1.0.0',
        endpoints: {
          auth: {
            'POST /api/auth/register': 'Register a new user',
            'POST /api/auth/login': 'Login user',
            'POST /api/auth/refresh': 'Refresh access token',
            'POST /api/auth/logout': 'Logout user',
            'GET /api/auth/profile': 'Get user profile',
          },
          users: {
            'GET /api/users/profile': 'Get current user profile',
            'PUT /api/users/profile': 'Update user profile',
            'DELETE /api/users/account': 'Delete user account',
          },
          ollama: {
            'GET /api/ollama/models': 'List available models',
            'GET /api/ollama/models/:name': 'Get model information',
            'POST /api/ollama/pull': 'Pull/download a model',
            'DELETE /api/ollama/models/:name': 'Delete a model',
            'POST /api/ollama/chat': 'Send chat message',
            'GET /api/ollama/conversations': 'Get user conversations',
            'GET /api/ollama/conversations/:id': 'Get specific conversation',
            'PUT /api/ollama/conversations/:id': 'Update conversation',
            'DELETE /api/ollama/conversations/:id': 'Delete conversation',
          },
          comfyui: {
            'GET /api/comfyui/workflows': 'List user workflows',
            'POST /api/comfyui/workflows': 'Create new workflow',
            'GET /api/comfyui/workflows/:id': 'Get specific workflow',
            'PUT /api/comfyui/workflows/:id': 'Update workflow',
            'DELETE /api/comfyui/workflows/:id': 'Delete workflow',
            'POST /api/comfyui/execute': 'Execute workflow',
            'GET /api/comfyui/queue': 'Get queue status',
            'GET /api/comfyui/queue/:id': 'Get queue item',
            'DELETE /api/comfyui/queue/:id': 'Cancel queue item',
            'GET /api/comfyui/nodes': 'Get available nodes',
            'GET /api/comfyui/history': 'Get execution history',
          },
          augment: {
            'GET /api/augment/projects': 'List user projects',
            'POST /api/augment/projects': 'Create new project',
            'GET /api/augment/projects/:id': 'Get specific project',
            'PUT /api/augment/projects/:id': 'Update project',
            'DELETE /api/augment/projects/:id': 'Delete project',
            'GET /api/augment/projects/:id/files': 'Get project files',
            'POST /api/augment/projects/:id/files': 'Create new file',
            'GET /api/augment/files/:id': 'Get specific file',
            'PUT /api/augment/files/:id': 'Update file',
            'DELETE /api/augment/files/:id': 'Delete file',
            'POST /api/augment/suggestions': 'Get code suggestions',
            'POST /api/augment/analyze': 'Analyze code',
          },
          system: {
            'GET /api/system/status': 'Get system status',
            'GET /api/system/metrics': 'Get system metrics',
            'GET /api/system/health': 'Get health check',
          },
        },
        websocket: {
          events: {
            'chat_message': 'Send chat message',
            'chat_stream': 'Receive streaming chat response',
            'queue_update': 'Receive queue status updates',
            'workflow_progress': 'Receive workflow execution progress',
            'system_status': 'Receive system status updates',
            'notification': 'Receive general notifications',
          },
        },
      });
    });
  }
}
