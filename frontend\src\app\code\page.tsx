'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Plus, 
  Save, 
  Play, 
  FolderOpen, 
  FileText, 
  Search,
  Lightbulb,
  Bug,
  Loader2
} from 'lucide-react';
import { augmentApi } from '@/lib/api';
import { getLanguageFromExtension } from '@/lib/utils';
import { toast } from 'react-hot-toast';

// Dynamically import Monaco Editor to avoid SSR issues
const MonacoEditor = dynamic(() => import('@monaco-editor/react'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  ),
});

export default function CodePage() {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState('javascript');
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false);
  const [isCreateFileDialogOpen, setIsCreateFileDialogOpen] = useState(false);
  const [newProjectData, setNewProjectData] = useState({
    name: '',
    path: '',
    language: 'javascript',
    framework: '',
  });
  const [newFileData, setNewFileData] = useState({
    name: '',
    content: '',
    language: 'javascript',
  });
  const queryClient = useQueryClient();

  // Fetch projects
  const { data: projectsData, isLoading: projectsLoading } = useQuery({
    queryKey: ['augment', 'projects'],
    queryFn: () => augmentApi.getProjects({ limit: 50 }),
  });

  // Fetch project files
  const { data: filesData } = useQuery({
    queryKey: ['augment', 'project-files', selectedProject],
    queryFn: () => selectedProject ? augmentApi.getProjectFiles(selectedProject) : null,
    enabled: !!selectedProject,
  });

  // Fetch specific file
  const { data: fileData } = useQuery({
    queryKey: ['augment', 'file', selectedFile],
    queryFn: () => selectedFile ? augmentApi.getFile(selectedFile) : null,
    enabled: !!selectedFile,
  });

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: augmentApi.createProject,
    onSuccess: () => {
      toast.success('Project created successfully');
      queryClient.invalidateQueries({ queryKey: ['augment', 'projects'] });
      setIsCreateProjectDialogOpen(false);
      setNewProjectData({ name: '', path: '', language: 'javascript', framework: '' });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create project');
    },
  });

  // Create file mutation
  const createFileMutation = useMutation({
    mutationFn: ({ projectId, data }: { projectId: string; data: any }) =>
      augmentApi.createFile(projectId, data),
    onSuccess: () => {
      toast.success('File created successfully');
      queryClient.invalidateQueries({ queryKey: ['augment', 'project-files', selectedProject] });
      setIsCreateFileDialogOpen(false);
      setNewFileData({ name: '', content: '', language: 'javascript' });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create file');
    },
  });

  // Update file mutation
  const updateFileMutation = useMutation({
    mutationFn: ({ fileId, data }: { fileId: string; data: any }) =>
      augmentApi.updateFile(fileId, data),
    onSuccess: () => {
      toast.success('File saved successfully');
      queryClient.invalidateQueries({ queryKey: ['augment', 'file', selectedFile] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save file');
    },
  });

  // Get code suggestions mutation
  const getSuggestionsMutation = useMutation({
    mutationFn: augmentApi.getSuggestions,
    onSuccess: (data) => {
      toast.success(`Found ${data.suggestions?.length || 0} suggestions`);
      // TODO: Display suggestions in UI
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to get suggestions');
    },
  });

  // Analyze code mutation
  const analyzeCodeMutation = useMutation({
    mutationFn: augmentApi.analyzeCode,
    onSuccess: (data) => {
      toast.success('Code analysis completed');
      // TODO: Display analysis results in UI
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to analyze code');
    },
  });

  const projects = projectsData?.data?.items || [];
  const files = filesData?.data?.files || [];

  // Load file content when file is selected
  useEffect(() => {
    if (fileData?.data?.file) {
      setCode(fileData.data.file.content);
      setLanguage(fileData.data.file.language);
    }
  }, [fileData]);

  const handleCreateProject = () => {
    if (!newProjectData.name || !newProjectData.path) {
      toast.error('Please fill in all required fields');
      return;
    }
    createProjectMutation.mutate(newProjectData);
  };

  const handleCreateFile = () => {
    if (!selectedProject || !newFileData.name) {
      toast.error('Please select a project and enter a file name');
      return;
    }
    
    const extension = newFileData.name.split('.').pop() || '';
    const detectedLanguage = getLanguageFromExtension(extension);
    
    createFileMutation.mutate({
      projectId: selectedProject,
      data: {
        ...newFileData,
        language: detectedLanguage,
      },
    });
  };

  const handleSaveFile = () => {
    if (!selectedFile) {
      toast.error('No file selected');
      return;
    }
    
    updateFileMutation.mutate({
      fileId: selectedFile,
      data: { content: code },
    });
  };

  const handleGetSuggestions = () => {
    if (!code) {
      toast.error('No code to analyze');
      return;
    }
    
    getSuggestionsMutation.mutate({
      code,
      language,
      cursorPosition: { line: 1, column: 1 }, // TODO: Get actual cursor position
    });
  };

  const handleAnalyzeCode = () => {
    if (!code) {
      toast.error('No code to analyze');
      return;
    }
    
    analyzeCodeMutation.mutate({
      code,
      language,
    });
  };

  return (
    <div className="flex h-full">
      {/* Sidebar - Projects and Files */}
      <div className="w-80 border-r bg-card">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-semibold">Projects</h2>
            <Dialog open={isCreateProjectDialogOpen} onOpenChange={setIsCreateProjectDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Project</DialogTitle>
                  <DialogDescription>
                    Create a new code project to organize your files.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="projectName">Project Name</Label>
                    <Input
                      id="projectName"
                      placeholder="My Project"
                      value={newProjectData.name}
                      onChange={(e) => setNewProjectData(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="projectPath">Project Path</Label>
                    <Input
                      id="projectPath"
                      placeholder="/path/to/project"
                      value={newProjectData.path}
                      onChange={(e) => setNewProjectData(prev => ({ ...prev, path: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="projectLanguage">Primary Language</Label>
                    <Select
                      value={newProjectData.language}
                      onValueChange={(value) => setNewProjectData(prev => ({ ...prev, language: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="javascript">JavaScript</SelectItem>
                        <SelectItem value="typescript">TypeScript</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="java">Java</SelectItem>
                        <SelectItem value="cpp">C++</SelectItem>
                        <SelectItem value="go">Go</SelectItem>
                        <SelectItem value="rust">Rust</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsCreateProjectDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateProject} disabled={createProjectMutation.isPending}>
                      {createProjectMutation.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Create Project
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
          
          <Select value={selectedProject || ''} onValueChange={setSelectedProject}>
            <SelectTrigger>
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {projects.map((project: any) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedProject && (
          <div className="p-4 border-b">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Files</h3>
              <Dialog open={isCreateFileDialogOpen} onOpenChange={setIsCreateFileDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New File</DialogTitle>
                    <DialogDescription>
                      Create a new file in the selected project.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="fileName">File Name</Label>
                      <Input
                        id="fileName"
                        placeholder="index.js"
                        value={newFileData.name}
                        onChange={(e) => setNewFileData(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsCreateFileDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleCreateFile} disabled={createFileMutation.isPending}>
                        {createFileMutation.isPending && (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        Create File
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="space-y-1">
              {files.map((file: any) => (
                <div
                  key={file.id}
                  className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-colors ${
                    selectedFile === file.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                  onClick={() => setSelectedFile(file.id)}
                >
                  <FileText className="h-4 w-4" />
                  <span className="text-sm truncate">{file.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b bg-card">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold">Code Editor</h1>
              {selectedFile && (
                <p className="text-sm text-muted-foreground">
                  Editing: {files.find((f: any) => f.id === selectedFile)?.name}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGetSuggestions}
                disabled={!code || getSuggestionsMutation.isPending}
              >
                {getSuggestionsMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Lightbulb className="mr-2 h-4 w-4" />
                )}
                Suggestions
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAnalyzeCode}
                disabled={!code || analyzeCodeMutation.isPending}
              >
                {analyzeCodeMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Bug className="mr-2 h-4 w-4" />
                )}
                Analyze
              </Button>
              <Button
                size="sm"
                onClick={handleSaveFile}
                disabled={!selectedFile || updateFileMutation.isPending}
              >
                {updateFileMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Save
              </Button>
            </div>
          </div>
        </div>

        {/* Editor */}
        <div className="flex-1">
          {selectedFile ? (
            <MonacoEditor
              height="100%"
              language={language}
              value={code}
              onChange={(value) => setCode(value || '')}
              theme="vs-dark"
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FolderOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No file selected</h3>
                <p className="text-muted-foreground">
                  Select a project and file to start coding
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
