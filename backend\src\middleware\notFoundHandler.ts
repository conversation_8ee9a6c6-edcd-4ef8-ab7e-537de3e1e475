import { Request, Response } from 'express';
import { logError } from '@/utils/logger';
import { ApiResponse } from '@shared/types';

export function notFoundHandler(req: Request, res: Response): void {
  const message = `Route ${req.method} ${req.path} not found`;
  
  logError(message, undefined, {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  const response: ApiResponse = {
    success: false,
    error: message,
  };

  res.status(404).json(response);
}
