# AI Development Stack GUI

A comprehensive web application that serves as a unified frontend for managing and interacting with a local AI development stack, integrating Ollama, ComfyUI, and Augment Code.

## Features

### 🤖 Ollama Integration
- Model management (install, remove, list)
- Real-time chat interface with multiple models
- Model configuration and parameter adjustment
- Resource usage monitoring

### 🎨 ComfyUI Integration
- Visual workflow builder/editor
- Queue management for generation tasks
- Image gallery and result viewing
- Node library and custom node management

### 💻 Augment Code Integration
- Code completion and suggestions
- Project file browser and editor
- AI-powered code analysis and refactoring

## Technology Stack

### Frontend
- **Next.js 14** with TypeScript
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Socket.io-client** for real-time communication
- **Monaco Editor** for code editing

### Backend
- **Node.js** with Express and TypeScript
- **Prisma ORM** with PostgreSQL
- **Socket.io** for WebSocket connections
- **JWT** authentication with bcrypt
- **<PERSON>** for logging

### Infrastructure
- **Docker** and **docker-compose** for containerization
- **PostgreSQL** database
- **Redis** for session storage and caching

## Project Structure

```
ai-dev-stack-gui/
├── frontend/                 # Next.js frontend application
├── backend/                  # Express.js backend API
├── shared/                   # Shared types and utilities
├── docker-compose.yml        # Docker services configuration
├── .env.example             # Environment variables template
└── docs/                    # Documentation
```

## Quick Start

1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Run with Docker: `docker-compose up -d`
4. Access the application at `http://localhost:3000`

## Development Setup

See [docs/development.md](docs/development.md) for detailed setup instructions.

## API Documentation

API documentation is available at `http://localhost:3001/api/docs` when running in development mode.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for contribution guidelines.

## License

MIT License - see [LICENSE](LICENSE) for details.
