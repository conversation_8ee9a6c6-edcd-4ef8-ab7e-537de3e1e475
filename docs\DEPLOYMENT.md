# Deployment Guide

This guide covers deploying the AI Development Stack GUI to production environments.

## Prerequisites

### System Requirements
- **CPU**: 4+ cores recommended
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB minimum, SSD recommended
- **Network**: Stable internet connection

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL (for SSL certificates)

### External Services
- **Ollama**: Running on accessible host
- **ComfyUI**: Running on accessible host
- **Domain**: For production deployment (optional)

## Quick Production Deployment

### 1. Clone Repository
```bash
git clone <repository-url>
cd ai-development-stack-gui
```

### 2. Configure Environment
```bash
# Copy production environment template
cp .env.production.example .env.production

# Edit configuration
nano .env.production
```

### 3. Deploy
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Deploy to production
./scripts/deploy.sh production deploy
```

## Detailed Configuration

### Environment Variables

#### Required Variables
```env
# Database
POSTGRES_PASSWORD=secure_password_here
DATABASE_URL=********************************************/ai_dev_stack

# Redis
REDIS_PASSWORD=secure_redis_password
REDIS_URL=redis://:password@redis:6379

# JWT Security
JWT_SECRET=your_64_character_secret_key_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# External Services
OLLAMA_BASE_URL=http://ollama-server:11434
COMFYUI_BASE_URL=http://comfyui-server:8188

# Application URLs
NEXT_PUBLIC_API_URL=https://your-domain.com/api
NEXT_PUBLIC_WS_URL=wss://your-domain.com
CORS_ORIGIN=https://your-domain.com
```

#### Optional Variables
```env
# Monitoring
GRAFANA_PASSWORD=admin_password
LOG_LEVEL=info

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Performance
NODE_OPTIONS=--max-old-space-size=2048
UV_THREADPOOL_SIZE=128
```

### SSL Configuration

#### Option 1: Self-Signed Certificates (Development)
```bash
# Generate self-signed certificates
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
```

#### Option 2: Let's Encrypt (Production)
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificates
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
```

#### Option 3: Custom Certificates
```bash
# Copy your certificates
cp your-certificate.crt nginx/ssl/cert.pem
cp your-private-key.key nginx/ssl/key.pem
```

## Deployment Methods

### Method 1: Docker Compose (Recommended)

#### Development Deployment
```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Production Deployment
```bash
# Start production environment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Scale services
docker-compose up -d --scale backend=3 --scale frontend=2

# Update services
docker-compose pull
docker-compose up -d
```

### Method 2: Manual Deployment

#### Backend Deployment
```bash
cd backend

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Run database migrations
npx prisma migrate deploy

# Start application
npm start
```

#### Frontend Deployment
```bash
cd frontend

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Start application
npm start
```

### Method 3: Kubernetes (Advanced)

#### Prerequisites
- Kubernetes cluster
- kubectl configured
- Helm (optional)

#### Deploy with kubectl
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n ai-dev-stack

# View logs
kubectl logs -f deployment/backend -n ai-dev-stack
```

## Monitoring and Maintenance

### Health Checks

#### Application Health
```bash
# Check backend health
curl http://localhost:3001/health

# Check frontend health
curl http://localhost:3000/api/health

# Check database connection
docker-compose exec postgres pg_isready -U postgres
```

#### Service Status
```bash
# View running containers
docker-compose ps

# Check resource usage
docker stats

# View logs
docker-compose logs -f [service_name]
```

### Monitoring Setup

#### Prometheus + Grafana
```bash
# Start monitoring stack
docker-compose --profile monitoring up -d

# Access Grafana
open http://localhost:3001
# Default credentials: admin/admin
```

#### Log Aggregation
```bash
# Start logging stack
docker-compose --profile logging up -d

# View aggregated logs
docker-compose logs fluentd
```

### Backup and Recovery

#### Automated Backups
```bash
# Create backup script
cat > scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup database
docker-compose exec -T postgres pg_dump -U postgres ai_dev_stack > "$BACKUP_DIR/database.sql"

# Backup uploads
cp -r backend/uploads "$BACKUP_DIR/"

# Compress backup
tar -czf "$BACKUP_DIR.tar.gz" "$BACKUP_DIR"
rm -rf "$BACKUP_DIR"
EOF

chmod +x scripts/backup.sh

# Schedule with cron
echo "0 2 * * * /path/to/scripts/backup.sh" | crontab -
```

#### Manual Backup
```bash
# Backup database
docker-compose exec postgres pg_dump -U postgres ai_dev_stack > backup.sql

# Backup uploads
tar -czf uploads_backup.tar.gz backend/uploads
```

#### Restore from Backup
```bash
# Restore database
docker-compose exec -T postgres psql -U postgres -d ai_dev_stack < backup.sql

# Restore uploads
tar -xzf uploads_backup.tar.gz -C backend/
```

## Performance Optimization

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_workflows_user_id ON workflows(user_id);
CREATE INDEX idx_queue_items_user_id ON queue_items(user_id);
```

### Redis Configuration
```conf
# redis.conf optimizations
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### Nginx Optimization
```nginx
# nginx.conf optimizations
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
client_max_body_size 50M;
gzip on;
gzip_comp_level 6;
```

## Security Considerations

### Network Security
- Use HTTPS in production
- Configure firewall rules
- Limit database access
- Use VPN for admin access

### Application Security
- Strong JWT secrets
- Rate limiting enabled
- Input validation
- CORS configuration
- Security headers

### Container Security
- Non-root user in containers
- Read-only file systems
- Resource limits
- Security scanning

## Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check database status
docker-compose exec postgres pg_isready -U postgres

# Check connection string
echo $DATABASE_URL

# Reset database
docker-compose down postgres
docker volume rm ai-dev-stack_postgres_data
docker-compose up -d postgres
```

#### External Service Unavailable
```bash
# Test Ollama connection
curl http://ollama-server:11434/api/tags

# Test ComfyUI connection
curl http://comfyui-server:8188/queue

# Check network connectivity
docker-compose exec backend ping ollama-server
```

#### High Memory Usage
```bash
# Check memory usage
docker stats

# Restart services
docker-compose restart

# Scale down if needed
docker-compose up -d --scale backend=1
```

### Log Analysis
```bash
# View application logs
docker-compose logs -f backend

# Search for errors
docker-compose logs backend | grep ERROR

# Monitor real-time logs
docker-compose logs -f --tail=100
```

## Scaling

### Horizontal Scaling
```bash
# Scale backend services
docker-compose up -d --scale backend=3

# Scale frontend services
docker-compose up -d --scale frontend=2

# Use load balancer
# Configure nginx upstream with multiple backends
```

### Vertical Scaling
```yaml
# docker-compose.prod.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
```

### Database Scaling
- Read replicas for read-heavy workloads
- Connection pooling
- Query optimization
- Caching strategies

## Maintenance

### Regular Tasks
- Update dependencies
- Rotate logs
- Clean up old images
- Monitor disk space
- Review security logs

### Update Process
```bash
# Pull latest changes
git pull origin main

# Rebuild images
docker-compose build

# Deploy updates
./scripts/deploy.sh production deploy

# Verify deployment
./scripts/deploy.sh production status
```

### Rollback Process
```bash
# Rollback to previous version
./scripts/deploy.sh production rollback

# Verify rollback
docker-compose ps
```
