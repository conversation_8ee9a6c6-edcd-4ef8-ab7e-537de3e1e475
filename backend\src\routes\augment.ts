import { Router } from 'express';
import { authenticate } from '@/middleware/auth';
import { externalApiRateLimiter } from '@/middleware/rateLimiter';
import { asyncHandler, NotFoundError, ExternalServiceError } from '@/middleware/errorHandler';
import { getPrismaClient, createPaginationQuery, createPaginationResult } from '@/utils/database';
import { augmentCodeValidation, fileValidation, paginationValidation } from '@shared/utils/validation';
import { logExternalService } from '@/utils/logger';
import { AugmentCodeService } from '@/services/augmentCodeService';
import { ApiResponse, Project, File as ProjectFile, CodeSuggestion } from '@shared/types';

const router = Router();
const prisma = getPrismaClient();
const augmentCodeService = new AugmentCodeService();

// Apply authentication and rate limiting to all routes
router.use(authenticate);
router.use(externalApiRateLimiter);

// List user projects
router.get('/projects', asyncHandler(async (req, res) => {
  const query = paginationValidation.parse(req.query);
  const userId = req.user!.id;
  const { skip, take, orderBy, page, limit } = createPaginationQuery(query);

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where: { userId },
      skip,
      take,
      orderBy: orderBy || { updatedAt: 'desc' },
      include: {
        _count: {
          select: { files: true },
        },
      },
    }),
    prisma.project.count({ where: { userId } }),
  ]);

  const paginatedResult = createPaginationResult(projects, total, page, limit);

  const response: ApiResponse<typeof paginatedResult> = {
    success: true,
    data: paginatedResult,
  };

  res.json(response);
}));

// Create new project
router.post('/projects', asyncHandler(async (req, res) => {
  const validatedData = augmentCodeValidation.createProject.parse(req.body);
  const userId = req.user!.id;

  const project = await prisma.project.create({
    data: {
      ...validatedData,
      userId,
    },
  });

  const response: ApiResponse<{ project: any }> = {
    success: true,
    data: { project },
    message: 'Project created successfully',
  };

  res.json(response);
}));

// Get specific project
router.get('/projects/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const project = await prisma.project.findFirst({
    where: { id, userId },
    include: {
      files: {
        orderBy: { name: 'asc' },
      },
      _count: {
        select: { files: true },
      },
    },
  });

  if (!project) {
    throw new NotFoundError('Project not found');
  }

  const response: ApiResponse<{ project: any }> = {
    success: true,
    data: { project },
  };

  res.json(response);
}));

// Update project
router.put('/projects/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const validatedData = augmentCodeValidation.updateProject.parse(req.body);

  const project = await prisma.project.findFirst({
    where: { id, userId },
  });

  if (!project) {
    throw new NotFoundError('Project not found');
  }

  const updatedProject = await prisma.project.update({
    where: { id },
    data: validatedData,
  });

  const response: ApiResponse<{ project: any }> = {
    success: true,
    data: { project: updatedProject },
    message: 'Project updated successfully',
  };

  res.json(response);
}));

// Delete project
router.delete('/projects/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const project = await prisma.project.findFirst({
    where: { id, userId },
  });

  if (!project) {
    throw new NotFoundError('Project not found');
  }

  await prisma.project.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Project deleted successfully',
  };

  res.json(response);
}));

// Get project files
router.get('/projects/:id/files', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  // Verify project ownership
  const project = await prisma.project.findFirst({
    where: { id, userId },
  });

  if (!project) {
    throw new NotFoundError('Project not found');
  }

  const files = await prisma.file.findMany({
    where: { projectId: id },
    orderBy: { name: 'asc' },
  });

  const response: ApiResponse<{ files: any[] }> = {
    success: true,
    data: { files },
  };

  res.json(response);
}));

// Create new file
router.post('/projects/:id/files', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const validatedData = fileValidation.createFile.parse(req.body);

  // Verify project ownership
  const project = await prisma.project.findFirst({
    where: { id, userId },
  });

  if (!project) {
    throw new NotFoundError('Project not found');
  }

  const file = await prisma.file.create({
    data: {
      ...validatedData,
      projectId: id,
    },
  });

  const response: ApiResponse<{ file: any }> = {
    success: true,
    data: { file },
    message: 'File created successfully',
  };

  res.json(response);
}));

// Get specific file
router.get('/files/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const file = await prisma.file.findFirst({
    where: {
      id,
      project: { userId },
    },
    include: {
      project: true,
    },
  });

  if (!file) {
    throw new NotFoundError('File not found');
  }

  const response: ApiResponse<{ file: any }> = {
    success: true,
    data: { file },
  };

  res.json(response);
}));

// Update file
router.put('/files/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const validatedData = fileValidation.updateFile.parse(req.body);

  const file = await prisma.file.findFirst({
    where: {
      id,
      project: { userId },
    },
  });

  if (!file) {
    throw new NotFoundError('File not found');
  }

  const updatedFile = await prisma.file.update({
    where: { id },
    data: validatedData,
  });

  const response: ApiResponse<{ file: any }> = {
    success: true,
    data: { file: updatedFile },
    message: 'File updated successfully',
  };

  res.json(response);
}));

// Delete file
router.delete('/files/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const file = await prisma.file.findFirst({
    where: {
      id,
      project: { userId },
    },
  });

  if (!file) {
    throw new NotFoundError('File not found');
  }

  await prisma.file.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'File deleted successfully',
  };

  res.json(response);
}));

// Get code suggestions
router.post('/suggestions', asyncHandler(async (req, res) => {
  const validatedData = augmentCodeValidation.getSuggestions.parse(req.body);

  try {
    const suggestions = await augmentCodeService.getCodeSuggestions(validatedData);

    const response: ApiResponse<{ suggestions: CodeSuggestion[] }> = {
      success: true,
      data: { suggestions },
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('AugmentCode', 'Failed to get code suggestions');
  }
}));

// Analyze code
router.post('/analyze', asyncHandler(async (req, res) => {
  const validatedData = augmentCodeValidation.analyzeCode.parse(req.body);

  try {
    const analysis = await augmentCodeService.analyzeCode(validatedData);

    const response: ApiResponse<{ analysis: any }> = {
      success: true,
      data: { analysis },
    };

    res.json(response);
  } catch (error) {
    throw new ExternalServiceError('AugmentCode', 'Failed to analyze code');
  }
}));

export default router;
