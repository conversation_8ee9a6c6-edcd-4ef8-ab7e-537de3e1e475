import { Server, Socket } from 'socket.io';
import { verifyToken } from '@/utils/auth';
import { getPrismaClient } from '@/utils/database';
import { logInfo, logError } from '@/utils/logger';
import { OllamaService } from '@/services/ollamaService';
import { ComfyUIService } from '@/services/comfyuiService';
import { AugmentCodeService } from '@/services/augmentCodeService';
import { WS_EVENTS } from '@shared/utils/constants';

const prisma = getPrismaClient();
const ollamaService = new OllamaService();
const comfyuiService = new ComfyUIService();
const augmentCodeService = new AugmentCodeService();

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
}

export function setupWebSocketHandlers(io: Server) {
  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const decoded = verifyToken(token);
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, email: true, username: true, role: true },
      });

      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user.id;
      socket.user = user;
      next();
    } catch (error) {
      logError('WebSocket authentication error', error);
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    logInfo(`User ${socket.user?.username} connected via WebSocket`);

    // Join user-specific room
    socket.join(`user:${socket.userId}`);

    // Send authentication confirmation
    socket.emit(WS_EVENTS.AUTHENTICATED, {
      userId: socket.userId,
      username: socket.user?.username,
      timestamp: new Date().toISOString(),
    });

    // Chat message handler
    socket.on(WS_EVENTS.CHAT_MESSAGE, async (data) => {
      try {
        const { message, model, conversationId, stream = true } = data;

        if (!message || !model) {
          socket.emit(WS_EVENTS.CHAT_ERROR, {
            error: 'Message and model are required',
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Create or get conversation
        let conversation;
        if (conversationId) {
          conversation = await prisma.conversation.findFirst({
            where: { id: conversationId, userId: socket.userId },
          });
        }

        if (!conversation) {
          conversation = await prisma.conversation.create({
            data: {
              title: message.slice(0, 50) + '...',
              model,
              userId: socket.userId!,
            },
          });
        }

        // Save user message
        await prisma.message.create({
          data: {
            role: 'user',
            content: message,
            conversationId: conversation.id,
          },
        });

        if (stream) {
          // Stream response
          const response = await ollamaService.chat({
            model,
            messages: [{ role: 'user', content: message }],
            stream: true,
          });

          // Emit streaming chunks
          socket.emit(WS_EVENTS.CHAT_STREAM, {
            conversationId: conversation.id,
            content: response.message.content,
            done: response.done,
            timestamp: new Date().toISOString(),
          });

          // Save assistant message when complete
          if (response.done) {
            await prisma.message.create({
              data: {
                role: 'assistant',
                content: response.message.content,
                conversationId: conversation.id,
              },
            });
          }
        } else {
          // Non-streaming response
          const response = await ollamaService.chat({
            model,
            messages: [{ role: 'user', content: message }],
            stream: false,
          });

          await prisma.message.create({
            data: {
              role: 'assistant',
              content: response.message.content,
              conversationId: conversation.id,
            },
          });

          socket.emit(WS_EVENTS.CHAT_RESPONSE, {
            conversationId: conversation.id,
            message: response.message,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        logError('Chat message error', error);
        socket.emit(WS_EVENTS.CHAT_ERROR, {
          error: 'Failed to process chat message',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Workflow execution handler
    socket.on(WS_EVENTS.WORKFLOW_EXECUTE, async (data) => {
      try {
        const { workflowId, inputs = {} } = data;

        if (!workflowId) {
          socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
            error: 'Workflow ID is required',
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Get workflow
        const workflow = await prisma.workflow.findFirst({
          where: { id: workflowId, userId: socket.userId },
        });

        if (!workflow) {
          socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
            error: 'Workflow not found',
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Convert workflow to ComfyUI format
        const prompt = comfyuiService.convertWorkflowToAPI(workflow as any);

        // Execute workflow
        const execution = await comfyuiService.executeWorkflow(prompt);

        // Create queue item
        const queueItem = await prisma.queueItem.create({
          data: {
            workflowId: workflow.id,
            status: 'PENDING',
            userId: socket.userId!,
            results: { prompt_id: execution.prompt_id },
          },
        });

        // Emit execution started
        socket.emit(WS_EVENTS.WORKFLOW_STARTED, {
          workflowId: workflow.id,
          queueItemId: queueItem.id,
          promptId: execution.prompt_id,
          timestamp: new Date().toISOString(),
        });

        // TODO: Monitor execution progress and emit updates
        // This would typically involve polling ComfyUI or setting up webhooks
        
      } catch (error) {
        logError('Workflow execution error', error);
        socket.emit(WS_EVENTS.WORKFLOW_ERROR, {
          error: 'Failed to execute workflow',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Code analysis handler
    socket.on(WS_EVENTS.CODE_ANALYZE, async (data) => {
      try {
        const { code, language, filePath } = data;

        if (!code || !language) {
          socket.emit(WS_EVENTS.CODE_ERROR, {
            error: 'Code and language are required',
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Analyze code
        const analysis = await augmentCodeService.analyzeCode({
          code,
          language,
          filePath,
        });

        socket.emit(WS_EVENTS.CODE_ANALYSIS_RESULT, {
          analysis,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logError('Code analysis error', error);
        socket.emit(WS_EVENTS.CODE_ERROR, {
          error: 'Failed to analyze code',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // System metrics subscription
    socket.on(WS_EVENTS.SUBSCRIBE_METRICS, () => {
      socket.join('metrics');
      logInfo(`User ${socket.user?.username} subscribed to metrics`);
    });

    socket.on(WS_EVENTS.UNSUBSCRIBE_METRICS, () => {
      socket.leave('metrics');
      logInfo(`User ${socket.user?.username} unsubscribed from metrics`);
    });

    // Disconnect handler
    socket.on('disconnect', (reason) => {
      logInfo(`User ${socket.user?.username} disconnected: ${reason}`);
    });

    // Error handler
    socket.on('error', (error) => {
      logError('WebSocket error', error);
    });
  });

  return io;
}

// Broadcast system metrics to subscribed clients
export function broadcastMetrics(io: Server, metrics: any) {
  io.to('metrics').emit(WS_EVENTS.METRICS_UPDATE, {
    metrics,
    timestamp: new Date().toISOString(),
  });
}

// Broadcast system status updates
export function broadcastSystemStatus(io: Server, services: any[]) {
  io.emit(WS_EVENTS.SYSTEM_STATUS, {
    services,
    timestamp: new Date().toISOString(),
  });
}

// Broadcast workflow progress updates
export function broadcastWorkflowProgress(io: Server, userId: string, data: any) {
  io.to(`user:${userId}`).emit(WS_EVENTS.WORKFLOW_PROGRESS, {
    ...data,
    timestamp: new Date().toISOString(),
  });
}

// Broadcast generation completion
export function broadcastGenerationComplete(io: Server, userId: string, data: any) {
  io.to(`user:${userId}`).emit(WS_EVENTS.GENERATION_COMPLETE, {
    ...data,
    timestamp: new Date().toISOString(),
  });
}
