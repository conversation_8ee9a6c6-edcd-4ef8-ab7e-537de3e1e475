import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
import { config } from '@/config';
import { logError, logSecurity } from '@/utils/logger';
import { ApiResponse } from '@shared/types';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error types
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = 'External service unavailable') {
    super(`${service}: ${message}`, 503, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
  }
}

// Error handler middleware
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let statusCode = 500;
  let message = 'Internal server error';
  let code = 'INTERNAL_ERROR';
  let details: any = undefined;

  // Handle different error types
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
  } else if (error instanceof ZodError) {
    statusCode = 400;
    message = 'Validation error';
    code = 'VALIDATION_ERROR';
    details = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }));
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        statusCode = 409;
        message = 'Unique constraint violation';
        code = 'DUPLICATE_ENTRY';
        details = { field: error.meta?.target };
        break;
      case 'P2025':
        statusCode = 404;
        message = 'Record not found';
        code = 'RECORD_NOT_FOUND';
        break;
      case 'P2003':
        statusCode = 400;
        message = 'Foreign key constraint violation';
        code = 'FOREIGN_KEY_ERROR';
        break;
      default:
        statusCode = 500;
        message = 'Database error';
        code = 'DATABASE_ERROR';
    }
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400;
    message = 'Invalid data provided';
    code = 'VALIDATION_ERROR';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    message = 'File upload error';
    code = 'FILE_UPLOAD_ERROR';
    
    if (error.message.includes('File too large')) {
      message = 'File size exceeds limit';
      code = 'FILE_TOO_LARGE';
    }
  }

  // Log error
  const errorMeta = {
    statusCode,
    code,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  };

  if (statusCode >= 500) {
    logError(`Server Error: ${message}`, error, errorMeta);
  } else if (statusCode === 401 || statusCode === 403) {
    logSecurity(`Security Error: ${message}`, (req as any).user?.id, req.ip, errorMeta);
  } else {
    logError(`Client Error: ${message}`, error, errorMeta);
  }

  // Prepare response
  const response: ApiResponse = {
    success: false,
    error: message,
    ...(code && { code }),
    ...(details && { details }),
  };

  // Add stack trace in development
  if (config.isDevelopment && !(error instanceof AppError)) {
    response.stack = error.stack;
  }

  res.status(statusCode).json(response);
}

// Async error wrapper
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// Not found handler
export function notFoundHandler(req: Request, res: Response): void {
  const message = `Route ${req.method} ${req.path} not found`;
  
  logError(message, undefined, {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json({
    success: false,
    error: message,
    code: 'ROUTE_NOT_FOUND',
  });
}

// Validation error helper
export function createValidationError(message: string, field?: string): ValidationError {
  const error = new ValidationError(message);
  if (field) {
    (error as any).field = field;
  }
  return error;
}

// Helper to throw errors
export function throwError(message: string, statusCode: number = 500, code?: string): never {
  throw new AppError(message, statusCode, code);
}

// Helper to throw not found error
export function throwNotFound(resource: string = 'Resource'): never {
  throw new NotFoundError(`${resource} not found`);
}

// Helper to throw validation error
export function throwValidation(message: string, field?: string): never {
  throw createValidationError(message, field);
}

// Helper to throw unauthorized error
export function throwUnauthorized(message?: string): never {
  throw new AuthenticationError(message);
}

// Helper to throw forbidden error
export function throwForbidden(message?: string): never {
  throw new AuthorizationError(message);
}

// Helper to throw conflict error
export function throwConflict(message?: string): never {
  throw new ConflictError(message);
}
