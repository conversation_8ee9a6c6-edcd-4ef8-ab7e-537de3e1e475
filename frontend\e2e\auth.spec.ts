import { test, expect } from '@playwright/test';

test.describe('Authentication E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should complete registration flow', async ({ page }) => {
    // Navigate to register page
    await page.click('text=Sign Up');
    await expect(page).toHaveURL('/register');

    // Fill registration form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="username-input"]', 'e2euser');
    await page.fill('[data-testid="firstName-input"]', 'E2E');
    await page.fill('[data-testid="lastName-input"]', 'User');
    await page.fill('[data-testid="password-input"]', 'E2ETest123!@#');
    await page.fill('[data-testid="confirmPassword-input"]', 'E2ETest123!@#');

    // Submit form
    await page.click('[data-testid="register-button"]');

    // Should redirect to dashboard after successful registration
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should complete login flow', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Sign In');
    await expect(page).toHaveURL('/login');

    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123!@#');

    // Submit form
    await page.click('[data-testid="login-button"]');

    // Should redirect to dashboard after successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should show validation errors for invalid input', async ({ page }) => {
    await page.click('text=Sign In');

    // Try to submit empty form
    await page.click('[data-testid="login-button"]');

    // Should show validation errors
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();

    // Try invalid email
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.click('[data-testid="login-button"]');
    await expect(page.locator('text=Invalid email format')).toBeVisible();
  });

  test('should handle login errors', async ({ page }) => {
    await page.click('text=Sign In');

    // Fill with invalid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    // Should show error message
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
    await expect(page).toHaveURL('/login'); // Should stay on login page
  });

  test('should complete logout flow', async ({ page }) => {
    // Login first
    await page.click('text=Sign In');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123!@#');
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL('/dashboard');

    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('text=Logout');

    // Should redirect to home page
    await expect(page).toHaveURL('/');
    await expect(page.locator('text=Sign In')).toBeVisible();
  });

  test('should protect authenticated routes', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/dashboard');

    // Should redirect to login
    await expect(page).toHaveURL('/login');
  });

  test('should persist authentication across page reloads', async ({ page }) => {
    // Login
    await page.click('text=Sign In');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123!@#');
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL('/dashboard');

    // Reload page
    await page.reload();

    // Should still be authenticated
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    // This test would require mocking token expiration
    // For now, we'll test the refresh flow by manually clearing tokens
    
    // Login first
    await page.click('text=Sign In');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123!@#');
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL('/dashboard');

    // Clear localStorage to simulate token expiration
    await page.evaluate(() => {
      localStorage.clear();
    });

    // Navigate to a protected route
    await page.goto('/chat');

    // Should redirect to login
    await expect(page).toHaveURL('/login');
  });
});

test.describe('Password Strength Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/register');
  });

  test('should show password strength indicator', async ({ page }) => {
    const passwordInput = page.locator('[data-testid="password-input"]');
    
    // Weak password
    await passwordInput.fill('weak');
    await expect(page.locator('[data-testid="password-strength"]')).toContainText('Weak');

    // Medium password
    await passwordInput.fill('Medium123');
    await expect(page.locator('[data-testid="password-strength"]')).toContainText('Medium');

    // Strong password
    await passwordInput.fill('StrongPassword123!@#');
    await expect(page.locator('[data-testid="password-strength"]')).toContainText('Strong');
  });

  test('should show password requirements', async ({ page }) => {
    await page.fill('[data-testid="password-input"]', 'test');

    // Should show specific requirements
    await expect(page.locator('text=Add uppercase letters')).toBeVisible();
    await expect(page.locator('text=Add numbers')).toBeVisible();
    await expect(page.locator('text=Add special characters')).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    await page.fill('[data-testid="password-input"]', 'Test123!@#');
    await page.fill('[data-testid="confirmPassword-input"]', 'Different123!@#');
    await page.click('[data-testid="register-button"]');

    await expect(page.locator('text=Passwords don\'t match')).toBeVisible();
  });
});

test.describe('Form Accessibility', () => {
  test('should be keyboard navigable', async ({ page }) => {
    await page.goto('/login');

    // Tab through form elements
    await page.keyboard.press('Tab'); // Email input
    await expect(page.locator('[data-testid="email-input"]')).toBeFocused();

    await page.keyboard.press('Tab'); // Password input
    await expect(page.locator('[data-testid="password-input"]')).toBeFocused();

    await page.keyboard.press('Tab'); // Submit button
    await expect(page.locator('[data-testid="login-button"]')).toBeFocused();

    // Submit with Enter
    await page.keyboard.press('Enter');
    // Should show validation errors since form is empty
  });

  test('should have proper ARIA labels', async ({ page }) => {
    await page.goto('/login');

    // Check for proper labels
    await expect(page.locator('[data-testid="email-input"]')).toHaveAttribute('aria-label', /email/i);
    await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('aria-label', /password/i);
  });

  test('should announce errors to screen readers', async ({ page }) => {
    await page.goto('/login');
    
    await page.click('[data-testid="login-button"]');

    // Error messages should have proper ARIA attributes
    const emailError = page.locator('text=Email is required');
    await expect(emailError).toHaveAttribute('role', 'alert');
  });
});

test.describe('Mobile Responsiveness', () => {
  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/login');

    // Form should be properly sized
    const form = page.locator('[data-testid="login-form"]');
    await expect(form).toBeVisible();

    // Inputs should be touch-friendly
    const emailInput = page.locator('[data-testid="email-input"]');
    const boundingBox = await emailInput.boundingBox();
    expect(boundingBox?.height).toBeGreaterThan(44); // Minimum touch target size
  });

  test('should handle mobile keyboard', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/login');

    // Email input should trigger email keyboard
    const emailInput = page.locator('[data-testid="email-input"]');
    await expect(emailInput).toHaveAttribute('type', 'email');
    await expect(emailInput).toHaveAttribute('inputmode', 'email');
  });
});

test.describe('Performance', () => {
  test('should load login page quickly', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;

    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('should handle form submission without blocking UI', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'demo123!@#');

    // Click submit and immediately check if form is still responsive
    await page.click('[data-testid="login-button"]');
    
    // Button should show loading state
    await expect(page.locator('[data-testid="login-button"]')).toContainText(/signing in/i);
    
    // Form should still be interactive (not completely frozen)
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
  });
});
