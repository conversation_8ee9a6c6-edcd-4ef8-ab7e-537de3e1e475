{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "node", "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}