import { z } from 'zod';

// User Validation Schemas
export const userValidation = {
  register: z.object({
    email: z.string().email('Invalid email address'),
    username: z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(30, 'Username must be less than 30 characters')
      .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    firstName: z.string().max(50, 'First name must be less than 50 characters').optional(),
    lastName: z.string().max(50, 'Last name must be less than 50 characters').optional(),
  }),
  
  login: z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(1, 'Password is required'),
  }),
  
  updateProfile: z.object({
    firstName: z.string().max(50, 'First name must be less than 50 characters').optional(),
    lastName: z.string().max(50, 'Last name must be less than 50 characters').optional(),
    avatar: z.string().url('Invalid avatar URL').optional(),
  }),
};

// Chat Validation Schemas
export const chatValidation = {
  sendMessage: z.object({
    message: z.string()
      .min(1, 'Message cannot be empty')
      .max(4000, 'Message must be less than 4000 characters'),
    model: z.string().min(1, 'Model is required'),
    conversationId: z.string().uuid('Invalid conversation ID').optional(),
    stream: z.boolean().optional(),
  }),
  
  createConversation: z.object({
    title: z.string()
      .min(1, 'Title is required')
      .max(100, 'Title must be less than 100 characters'),
    model: z.string().min(1, 'Model is required'),
  }),
  
  updateConversation: z.object({
    title: z.string()
      .min(1, 'Title is required')
      .max(100, 'Title must be less than 100 characters'),
  }),
};

// Ollama Validation Schemas
export const ollamaValidation = {
  pullModel: z.object({
    name: z.string().min(1, 'Model name is required'),
    stream: z.boolean().optional(),
  }),
  
  deleteModel: z.object({
    name: z.string().min(1, 'Model name is required'),
  }),
  
  modelInfo: z.object({
    name: z.string().min(1, 'Model name is required'),
  }),
};

// ComfyUI Validation Schemas
export const comfyUIValidation = {
  createWorkflow: z.object({
    name: z.string()
      .min(1, 'Workflow name is required')
      .max(100, 'Workflow name must be less than 100 characters'),
    description: z.string()
      .max(500, 'Description must be less than 500 characters')
      .optional(),
    nodes: z.record(z.any()),
    links: z.array(z.tuple([z.number(), z.number(), z.number(), z.number(), z.number(), z.string()])),
    groups: z.array(z.any()),
    config: z.record(z.any()),
    version: z.string(),
    isPublic: z.boolean().optional(),
  }),
  
  updateWorkflow: z.object({
    name: z.string()
      .min(1, 'Workflow name is required')
      .max(100, 'Workflow name must be less than 100 characters')
      .optional(),
    description: z.string()
      .max(500, 'Description must be less than 500 characters')
      .optional(),
    nodes: z.record(z.any()).optional(),
    links: z.array(z.tuple([z.number(), z.number(), z.number(), z.number(), z.number(), z.string()])).optional(),
    groups: z.array(z.any()).optional(),
    config: z.record(z.any()).optional(),
    version: z.string().optional(),
    isPublic: z.boolean().optional(),
  }),
  
  executeWorkflow: z.object({
    workflowId: z.string().uuid('Invalid workflow ID'),
    inputs: z.record(z.any()).optional(),
  }),
};

// Augment Code Validation Schemas
export const augmentCodeValidation = {
  createProject: z.object({
    name: z.string()
      .min(1, 'Project name is required')
      .max(100, 'Project name must be less than 100 characters'),
    path: z.string().min(1, 'Project path is required'),
    language: z.string().min(1, 'Language is required'),
    framework: z.string().optional(),
  }),
  
  updateProject: z.object({
    name: z.string()
      .min(1, 'Project name is required')
      .max(100, 'Project name must be less than 100 characters')
      .optional(),
    language: z.string().min(1, 'Language is required').optional(),
    framework: z.string().optional(),
  }),
  
  analyzeCode: z.object({
    code: z.string().min(1, 'Code is required'),
    language: z.string().min(1, 'Language is required'),
    filePath: z.string().optional(),
  }),
  
  getSuggestions: z.object({
    code: z.string().min(1, 'Code is required'),
    language: z.string().min(1, 'Language is required'),
    cursorPosition: z.object({
      line: z.number().min(0),
      column: z.number().min(0),
    }),
    context: z.string().optional(),
  }),
};

// File Validation Schemas
export const fileValidation = {
  upload: z.object({
    file: z.any().refine((file) => file instanceof File, 'File is required'),
    type: z.enum(['image', 'code', 'workflow', 'other']),
  }),
  
  createFile: z.object({
    name: z.string()
      .min(1, 'File name is required')
      .max(255, 'File name must be less than 255 characters'),
    content: z.string(),
    language: z.string().min(1, 'Language is required'),
    projectId: z.string().uuid('Invalid project ID'),
  }),
  
  updateFile: z.object({
    content: z.string(),
    name: z.string()
      .min(1, 'File name is required')
      .max(255, 'File name must be less than 255 characters')
      .optional(),
  }),
};

// Pagination Validation Schema
export const paginationValidation = z.object({
  page: z.coerce.number().min(1, 'Page must be at least 1').optional(),
  limit: z.coerce.number().min(1, 'Limit must be at least 1').max(100, 'Limit must be at most 100').optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  search: z.string().optional(),
});

// ID Validation Schemas
export const idValidation = {
  uuid: z.string().uuid('Invalid ID format'),
  objectId: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format'),
};

// Utility Functions
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize;
}
