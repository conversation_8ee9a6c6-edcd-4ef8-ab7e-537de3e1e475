// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  avatar    String?
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  conversations Conversation[]
  workflows     Workflow[]
  projects      Project[]
  queueItems    QueueItem[]
  refreshTokens RefreshToken[]

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model Conversation {
  id        String   @id @default(cuid())
  title     String
  model     String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(cuid())
  role           String // 'user', 'assistant', 'system'
  content        String
  conversationId String
  timestamp      DateTime @default(now())

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Workflow {
  id          String   @id @default(cuid())
  name        String
  description String?
  nodes       Json
  links       Json
  groups      Json
  config      Json
  version     String
  isPublic    Boolean  @default(false)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  queueItems QueueItem[]

  @@map("workflows")
}

model QueueItem {
  id          String      @id @default(cuid())
  workflowId  String
  status      QueueStatus @default(PENDING)
  progress    Int         @default(0)
  startedAt   DateTime?
  completedAt DateTime?
  error       String?
  results     Json?
  userId      String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("queue_items")
}

model Project {
  id        String   @id @default(cuid())
  name      String
  path      String
  language  String
  framework String?
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  files File[]

  @@map("projects")
}

model File {
  id           String   @id @default(cuid())
  projectId    String
  path         String
  name         String
  content      String
  language     String
  size         Int
  lastModified DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("files")
}

model SystemMetric {
  id        String   @id @default(cuid())
  cpu       Float
  memory    Float
  disk      Float
  network   Json
  timestamp DateTime @default(now())

  @@map("system_metrics")
}

model ServiceStatus {
  id           String   @id @default(cuid())
  name         String   @unique
  status       String // 'online', 'offline', 'error'
  url          String
  lastCheck    DateTime @default(now())
  responseTime Int?
  error        String?
  updatedAt    DateTime @updatedAt

  @@map("service_status")
}

enum UserRole {
  USER
  ADMIN
}

enum QueueStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}
