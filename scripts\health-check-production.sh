#!/bin/bash

# AI Development Stack GUI - Production Health Check Script
# This script performs comprehensive health checks on the production deployment

set -euo pipefail

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
readonly HEALTH_LOG="$PROJECT_ROOT/monitoring/logs/health-check-$(date +%Y%m%d).log"

# Health check configuration
readonly TIMEOUT=10
readonly MAX_RETRIES=3
readonly RETRY_DELAY=5

# Service endpoints
readonly FRONTEND_URL="http://localhost:3000"
readonly BACKEND_URL="http://localhost:3001"
readonly BACKEND_HEALTH_URL="http://localhost:3001/health"
readonly BACKEND_API_URL="http://localhost:3001/api"

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
    esac
    
    # Log to file if directory exists
    if [[ -d "$(dirname "$HEALTH_LOG")" ]]; then
        echo "[$timestamp] [$level] $message" >> "$HEALTH_LOG"
    fi
}

# Initialize health check
init_health_check() {
    log "INFO" "Starting comprehensive health check..."
    log "INFO" "Timestamp: $(date)"
    log "INFO" "Project root: $PROJECT_ROOT"
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$HEALTH_LOG")"
}

# Check if Docker is running
check_docker() {
    log "INFO" "Checking Docker status..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log "ERROR" "Docker is not installed"
        return 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log "ERROR" "Docker daemon is not running"
        return 1
    fi
    
    log "SUCCESS" "Docker is running"
    return 0
}

# Check Docker Compose
check_docker_compose() {
    log "INFO" "Checking Docker Compose..."
    
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        log "ERROR" "Docker Compose is not available"
        return 1
    fi
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log "ERROR" "Production compose file not found: $COMPOSE_FILE"
        return 1
    fi
    
    log "SUCCESS" "Docker Compose is available"
    return 0
}

# Check container status
check_containers() {
    log "INFO" "Checking container status..."
    
    local containers=("ai-stack-postgres-prod" "ai-stack-redis-prod" "ai-stack-backend-prod" "ai-stack-frontend-prod")
    local all_running=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            local status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null || echo "unknown")
            if [[ "$status" == "running" ]]; then
                log "SUCCESS" "✓ $container is running"
            else
                log "ERROR" "✗ $container is not running (status: $status)"
                all_running=false
            fi
        else
            log "ERROR" "✗ $container is not found"
            all_running=false
        fi
    done
    
    if [[ "$all_running" == "true" ]]; then
        log "SUCCESS" "All containers are running"
        return 0
    else
        log "ERROR" "Some containers are not running"
        return 1
    fi
}

# Check container health
check_container_health() {
    log "INFO" "Checking container health status..."
    
    local containers=("ai-stack-postgres-prod" "ai-stack-redis-prod" "ai-stack-backend-prod" "ai-stack-frontend-prod")
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            case "$health" in
                "healthy")
                    log "SUCCESS" "✓ $container is healthy"
                    ;;
                "unhealthy")
                    log "ERROR" "✗ $container is unhealthy"
                    all_healthy=false
                    ;;
                "starting")
                    log "WARNING" "⚠ $container is starting"
                    ;;
                "no-healthcheck")
                    log "INFO" "ℹ $container has no health check configured"
                    ;;
                *)
                    log "WARNING" "? $container health status unknown: $health"
                    ;;
            esac
        fi
    done
    
    if [[ "$all_healthy" == "true" ]]; then
        log "SUCCESS" "All containers with health checks are healthy"
        return 0
    else
        log "WARNING" "Some containers may have health issues"
        return 1
    fi
}

# Test HTTP endpoint with retries
test_endpoint() {
    local url="$1"
    local name="$2"
    local expected_status="${3:-200}"
    
    for ((i=1; i<=MAX_RETRIES; i++)); do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$TIMEOUT" "$url" 2>/dev/null || echo "000")
        
        if [[ "$status_code" == "$expected_status" ]]; then
            log "SUCCESS" "✓ $name is responding (HTTP $status_code)"
            return 0
        else
            log "WARNING" "⚠ $name attempt $i/$MAX_RETRIES failed (HTTP $status_code)"
            if [[ $i -lt $MAX_RETRIES ]]; then
                sleep "$RETRY_DELAY"
            fi
        fi
    done
    
    log "ERROR" "✗ $name is not responding after $MAX_RETRIES attempts"
    return 1
}

# Check database connectivity
check_database() {
    log "INFO" "Checking database connectivity..."
    
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
        log "SUCCESS" "✓ PostgreSQL is accepting connections"
        
        # Test database query
        if docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U postgres -d ai_dev_stack_prod -c "SELECT 1;" >/dev/null 2>&1; then
            log "SUCCESS" "✓ Database query test passed"
            return 0
        else
            log "ERROR" "✗ Database query test failed"
            return 1
        fi
    else
        log "ERROR" "✗ PostgreSQL is not accepting connections"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "INFO" "Checking Redis connectivity..."
    
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping >/dev/null 2>&1; then
        log "SUCCESS" "✓ Redis is responding to ping"
        
        # Test Redis operations
        if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli set healthcheck "$(date)" >/dev/null 2>&1 && \
           docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli get healthcheck >/dev/null 2>&1; then
            log "SUCCESS" "✓ Redis read/write test passed"
            docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli del healthcheck >/dev/null 2>&1
            return 0
        else
            log "ERROR" "✗ Redis read/write test failed"
            return 1
        fi
    else
        log "ERROR" "✗ Redis is not responding"
        return 1
    fi
}

# Check web services
check_web_services() {
    log "INFO" "Checking web services..."
    
    local frontend_ok=true
    local backend_ok=true
    local api_ok=true
    
    # Check frontend
    if ! test_endpoint "$FRONTEND_URL" "Frontend"; then
        frontend_ok=false
    fi
    
    # Check backend health endpoint
    if ! test_endpoint "$BACKEND_HEALTH_URL" "Backend Health"; then
        backend_ok=false
    fi
    
    # Check backend API
    if ! test_endpoint "$BACKEND_API_URL" "Backend API" "404"; then  # 404 is expected for base API path
        api_ok=false
    fi
    
    if [[ "$frontend_ok" == "true" ]] && [[ "$backend_ok" == "true" ]] && [[ "$api_ok" == "true" ]]; then
        log "SUCCESS" "All web services are responding"
        return 0
    else
        log "ERROR" "Some web services are not responding"
        return 1
    fi
}

# Check system resources
check_system_resources() {
    log "INFO" "Checking system resources..."
    
    # Check disk space
    local disk_usage=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log "ERROR" "✗ Disk usage is critical: ${disk_usage}%"
        return 1
    elif [[ $disk_usage -gt 80 ]]; then
        log "WARNING" "⚠ Disk usage is high: ${disk_usage}%"
    else
        log "SUCCESS" "✓ Disk usage is normal: ${disk_usage}%"
    fi
    
    # Check memory usage (if available)
    if command -v free >/dev/null 2>&1; then
        local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [[ $memory_usage -gt 90 ]]; then
            log "ERROR" "✗ Memory usage is critical: ${memory_usage}%"
            return 1
        elif [[ $memory_usage -gt 80 ]]; then
            log "WARNING" "⚠ Memory usage is high: ${memory_usage}%"
        else
            log "SUCCESS" "✓ Memory usage is normal: ${memory_usage}%"
        fi
    fi
    
    return 0
}

# Check log files for errors
check_logs() {
    log "INFO" "Checking recent logs for errors..."
    
    local error_count=0
    
    # Check Docker logs for errors
    if docker-compose -f "$COMPOSE_FILE" logs --tail=100 2>/dev/null | grep -i "error\|exception\|failed" >/dev/null; then
        error_count=$((error_count + 1))
        log "WARNING" "⚠ Found errors in Docker logs"
    fi
    
    # Check application logs if they exist
    if [[ -d "$PROJECT_ROOT/monitoring/logs" ]]; then
        local recent_errors=$(find "$PROJECT_ROOT/monitoring/logs" -name "*.log" -mtime -1 -exec grep -l -i "error\|exception\|failed" {} \; 2>/dev/null | wc -l)
        if [[ $recent_errors -gt 0 ]]; then
            error_count=$((error_count + recent_errors))
            log "WARNING" "⚠ Found errors in $recent_errors log files"
        fi
    fi
    
    if [[ $error_count -eq 0 ]]; then
        log "SUCCESS" "✓ No recent errors found in logs"
        return 0
    else
        log "WARNING" "⚠ Found $error_count potential issues in logs"
        return 1
    fi
}

# Generate health report
generate_health_report() {
    local overall_status="$1"
    
    log "INFO" "Generating health report..."
    
    local report_file="$PROJECT_ROOT/monitoring/health-report-$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "overall_status": "$overall_status",
  "checks": {
    "docker": "$(check_docker && echo "pass" || echo "fail")",
    "containers": "$(check_containers && echo "pass" || echo "fail")",
    "database": "$(check_database && echo "pass" || echo "fail")",
    "redis": "$(check_redis && echo "pass" || echo "fail")",
    "web_services": "$(check_web_services && echo "pass" || echo "fail")",
    "system_resources": "$(check_system_resources && echo "pass" || echo "fail")"
  },
  "services": {
    "frontend": "$FRONTEND_URL",
    "backend": "$BACKEND_URL",
    "database": "localhost:5432",
    "redis": "localhost:6379"
  }
}
EOF
    
    log "SUCCESS" "Health report generated: $report_file"
}

# Main health check function
main() {
    init_health_check
    
    local checks_passed=0
    local total_checks=7
    
    # Run all health checks
    check_docker && ((checks_passed++)) || true
    check_docker_compose && ((checks_passed++)) || true
    check_containers && ((checks_passed++)) || true
    check_container_health && ((checks_passed++)) || true
    check_database && ((checks_passed++)) || true
    check_redis && ((checks_passed++)) || true
    check_web_services && ((checks_passed++)) || true
    check_system_resources || true  # Don't count this as critical
    check_logs || true  # Don't count this as critical
    
    # Determine overall status
    local overall_status
    if [[ $checks_passed -eq $total_checks ]]; then
        overall_status="healthy"
        log "SUCCESS" "🎉 All critical health checks passed ($checks_passed/$total_checks)"
        generate_health_report "$overall_status"
        exit 0
    elif [[ $checks_passed -ge $((total_checks * 2 / 3)) ]]; then
        overall_status="degraded"
        log "WARNING" "⚠️  Some health checks failed ($checks_passed/$total_checks)"
        generate_health_report "$overall_status"
        exit 1
    else
        overall_status="unhealthy"
        log "ERROR" "❌ Critical health check failures ($checks_passed/$total_checks)"
        generate_health_report "$overall_status"
        exit 2
    fi
}

# Show usage
show_usage() {
    cat << EOF
AI Development Stack GUI - Production Health Check

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --help, -h          Show this help message
    --verbose, -v       Enable verbose output
    --json              Output results in JSON format
    --continuous        Run continuous health monitoring

EXAMPLES:
    $0                  # Run standard health check
    $0 --verbose        # Run with detailed output
    $0 --json           # Output JSON report
    $0 --continuous     # Monitor continuously

EXIT CODES:
    0    All checks passed (healthy)
    1    Some checks failed (degraded)
    2    Critical failures (unhealthy)
EOF
}

# Parse command line arguments
if [[ $# -gt 0 ]]; then
    case "$1" in
        --help|-h)
            show_usage
            exit 0
            ;;
        --verbose|-v)
            set -x
            ;;
        --json)
            # JSON output mode would be implemented here
            log "INFO" "JSON output mode not yet implemented"
            ;;
        --continuous)
            log "INFO" "Starting continuous health monitoring..."
            while true; do
                main
                sleep 60
            done
            ;;
        *)
            log "ERROR" "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
fi

# Run main function
main "$@"
