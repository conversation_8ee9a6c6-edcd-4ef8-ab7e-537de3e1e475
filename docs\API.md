# API Documentation

This document provides comprehensive documentation for the AI Development Stack GUI API.

## Base URL

```
http://localhost:3001/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "message": string,
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Authentication Endpoints

### Register User

```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token",
      "expiresIn": 900
    }
  }
}
```

### Login User

```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token",
      "expiresIn": 900
    }
  }
}
```

### Refresh Token

```http
POST /auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "refresh_token"
}
```

### Get User Profile

```http
GET /auth/profile
```

**Headers:**
```
Authorization: Bearer <access_token>
```

### Logout

```http
POST /auth/logout
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "refreshToken": "refresh_token"
}
```

## Ollama Endpoints

### List Models

```http
GET /ollama/models
```

**Response:**
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "name": "llama2:7b",
        "size": 3825819519,
        "digest": "sha256:...",
        "modified_at": "2024-01-01T00:00:00.000Z",
        "details": {
          "format": "gguf",
          "family": "llama",
          "parameter_size": "7B",
          "quantization_level": "Q4_0"
        }
      }
    ]
  }
}
```

### Get Model Info

```http
GET /ollama/models/:name
```

### Pull Model

```http
POST /ollama/pull
```

**Request Body:**
```json
{
  "name": "llama2:7b",
  "stream": false
}
```

### Delete Model

```http
DELETE /ollama/models/:name
```

### Send Chat Message

```http
POST /ollama/chat
```

**Request Body:**
```json
{
  "message": "Hello, how are you?",
  "model": "llama2:7b",
  "conversationId": "uuid",
  "stream": true
}
```

### Get Conversations

```http
GET /ollama/conversations?page=1&limit=20
```

### Get Conversation

```http
GET /ollama/conversations/:id
```

### Update Conversation

```http
PUT /ollama/conversations/:id
```

**Request Body:**
```json
{
  "title": "New conversation title"
}
```

### Delete Conversation

```http
DELETE /ollama/conversations/:id
```

## ComfyUI Endpoints

### List Workflows

```http
GET /comfyui/workflows?page=1&limit=20
```

### Create Workflow

```http
POST /comfyui/workflows
```

**Request Body:**
```json
{
  "name": "My Workflow",
  "description": "A sample workflow",
  "nodes": {
    "1": {
      "type": "LoadImage",
      "inputs": {},
      "outputs": {}
    }
  },
  "connections": [],
  "isPublic": false
}
```

### Get Workflow

```http
GET /comfyui/workflows/:id
```

### Update Workflow

```http
PUT /comfyui/workflows/:id
```

### Delete Workflow

```http
DELETE /comfyui/workflows/:id
```

### Execute Workflow

```http
POST /comfyui/execute
```

**Request Body:**
```json
{
  "workflowId": "uuid",
  "inputs": {}
}
```

### Get Queue

```http
GET /comfyui/queue
```

### Get Queue Item

```http
GET /comfyui/queue/:id
```

### Cancel Queue Item

```http
DELETE /comfyui/queue/:id
```

### Get Available Nodes

```http
GET /comfyui/nodes
```

### Get Execution History

```http
GET /comfyui/history?page=1&limit=20
```

## Augment Code Endpoints

### List Projects

```http
GET /augment/projects?page=1&limit=20
```

### Create Project

```http
POST /augment/projects
```

**Request Body:**
```json
{
  "name": "My Project",
  "path": "/path/to/project",
  "language": "javascript",
  "framework": "react"
}
```

### Get Project

```http
GET /augment/projects/:id
```

### Update Project

```http
PUT /augment/projects/:id
```

### Delete Project

```http
DELETE /augment/projects/:id
```

### Get Project Files

```http
GET /augment/projects/:id/files
```

### Create File

```http
POST /augment/projects/:id/files
```

**Request Body:**
```json
{
  "name": "index.js",
  "content": "console.log('Hello World');",
  "language": "javascript"
}
```

### Get File

```http
GET /augment/files/:id
```

### Update File

```http
PUT /augment/files/:id
```

**Request Body:**
```json
{
  "content": "console.log('Updated content');"
}
```

### Delete File

```http
DELETE /augment/files/:id
```

### Get Code Suggestions

```http
POST /augment/suggestions
```

**Request Body:**
```json
{
  "code": "function hello() {",
  "language": "javascript",
  "cursorPosition": {
    "line": 1,
    "column": 18
  },
  "context": "React component"
}
```

### Analyze Code

```http
POST /augment/analyze
```

**Request Body:**
```json
{
  "code": "function hello() { console.log('world'); }",
  "language": "javascript",
  "filePath": "/path/to/file.js"
}
```

## System Endpoints

### Get System Status

```http
GET /system/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": [
      {
        "name": "Database",
        "status": "online",
        "url": "postgresql://localhost:5432",
        "lastCheck": "2024-01-01T00:00:00.000Z",
        "responseTime": 5
      }
    ]
  }
}
```

### Get System Metrics

```http
GET /system/metrics
```

**Response:**
```json
{
  "success": true,
  "data": {
    "metrics": {
      "cpu": 25.5,
      "memory": 60.2,
      "disk": 45.8,
      "network": {
        "upload": 1024,
        "download": 2048
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### Health Check

```http
GET /system/health
```

## WebSocket Events

### Client to Server Events

#### Chat Message
```javascript
socket.emit('chat_message', {
  message: 'Hello',
  model: 'llama2:7b',
  conversationId: 'uuid',
  stream: true
});
```

#### Workflow Execute
```javascript
socket.emit('workflow_execute', {
  workflowId: 'uuid',
  inputs: {}
});
```

#### Code Analyze
```javascript
socket.emit('code_analyze', {
  code: 'function hello() {}',
  language: 'javascript'
});
```

### Server to Client Events

#### Chat Stream
```javascript
socket.on('chat_stream', (data) => {
  // data: { conversationId, content, done, timestamp }
});
```

#### Workflow Progress
```javascript
socket.on('workflow_progress', (data) => {
  // data: { workflowId, progress, status, timestamp }
});
```

#### Generation Complete
```javascript
socket.on('generation_complete', (data) => {
  // data: { workflowId, results, timestamp }
});
```

#### Metrics Update
```javascript
socket.on('metrics_update', (data) => {
  // data: { metrics, timestamp }
});
```

#### System Status
```javascript
socket.on('system_status', (data) => {
  // data: { services, timestamp }
});
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- Authentication endpoints: 5 requests per minute
- General API endpoints: 100 requests per minute
- External service endpoints: 20 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Pagination

List endpoints support pagination with these query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `search`: Search term
- `sortBy`: Sort field
- `sortOrder`: Sort direction (asc/desc)

Pagination response includes:
```json
{
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```
