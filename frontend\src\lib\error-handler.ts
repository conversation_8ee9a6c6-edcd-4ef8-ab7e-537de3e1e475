import { toast } from 'react-hot-toast';

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

export class AppError extends Error {
  public readonly code?: string;
  public readonly status?: number;
  public readonly details?: any;

  constructor(message: string, code?: string, status?: number, details?: any) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.status = status;
    this.details = details;
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class NetworkError extends AppError {
  constructor(message: string, status?: number) {
    super(message, 'NETWORK_ERROR', status);
    this.name = 'NetworkError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 'AUTH_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NOT_FOUND_ERROR', 404);
    this.name = 'NotFoundError';
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(service: string) {
    super(`${service} service is currently unavailable`, 'SERVICE_UNAVAILABLE', 503);
    this.name = 'ServiceUnavailableError';
  }
}

// Error handler for API responses
export function handleApiError(error: any): AppError {
  // If it's already an AppError, return as is
  if (error instanceof AppError) {
    return error;
  }

  // Handle fetch/axios errors
  if (error.response) {
    const { status, data } = error.response;
    const message = data?.error || data?.message || 'An error occurred';
    
    switch (status) {
      case 400:
        return new ValidationError(message, data?.details);
      case 401:
        return new AuthenticationError(message);
      case 403:
        return new AuthorizationError(message);
      case 404:
        return new NotFoundError(message);
      case 503:
        return new ServiceUnavailableError(data?.service || 'External service');
      default:
        return new AppError(message, 'API_ERROR', status);
    }
  }

  // Handle network errors
  if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
    return new NetworkError('Network connection failed. Please check your internet connection.');
  }

  // Handle timeout errors
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    return new NetworkError('Request timed out. Please try again.');
  }

  // Default error
  return new AppError(error.message || 'An unexpected error occurred', 'UNKNOWN_ERROR');
}

// Error display handler
export function displayError(error: Error | AppError, options?: {
  showToast?: boolean;
  logToConsole?: boolean;
  context?: string;
}) {
  const { showToast = true, logToConsole = true, context } = options || {};

  // Log to console in development
  if (logToConsole && process.env.NODE_ENV === 'development') {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error);
  }

  // Show toast notification
  if (showToast) {
    const message = error instanceof AppError ? error.message : 'An unexpected error occurred';
    
    if (error instanceof ValidationError) {
      toast.error(`Validation Error: ${message}`);
    } else if (error instanceof AuthenticationError) {
      toast.error('Please sign in to continue');
    } else if (error instanceof AuthorizationError) {
      toast.error('You don\'t have permission to perform this action');
    } else if (error instanceof NotFoundError) {
      toast.error('The requested resource was not found');
    } else if (error instanceof NetworkError) {
      toast.error('Network error. Please check your connection and try again.');
    } else if (error instanceof ServiceUnavailableError) {
      toast.error(message);
    } else {
      toast.error(message);
    }
  }

  // Log to error tracking service (in production)
  if (process.env.NODE_ENV === 'production') {
    logErrorToService(error, context);
  }
}

// Error logging service (mock implementation)
function logErrorToService(error: Error, context?: string) {
  // In a real application, you would send this to your error tracking service
  // like Sentry, LogRocket, Bugsnag, etc.
  const errorData = {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
    userId: undefined, // You would get this from your auth context
  };

  console.log('Error logged to service:', errorData);
}

// Retry utility for failed operations
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Don't retry on certain errors
      if (error instanceof AuthenticationError || 
          error instanceof AuthorizationError || 
          error instanceof ValidationError) {
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}

// Async error handler for React components
export function withAsyncErrorHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      const appError = handleApiError(error);
      displayError(appError);
      return undefined;
    }
  };
}

// Form validation error handler
export function handleFormErrors(error: any): Record<string, string> {
  if (error instanceof ValidationError && error.details) {
    const fieldErrors: Record<string, string> = {};
    
    if (Array.isArray(error.details)) {
      error.details.forEach((detail: any) => {
        if (detail.path && detail.message) {
          fieldErrors[detail.path.join('.')] = detail.message;
        }
      });
    } else if (typeof error.details === 'object') {
      Object.keys(error.details).forEach(key => {
        fieldErrors[key] = error.details[key];
      });
    }
    
    return fieldErrors;
  }

  return {};
}

// Global error handler for unhandled promise rejections
export function setupGlobalErrorHandlers() {
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      const error = handleApiError(event.reason);
      displayError(error, { 
        context: 'Unhandled Promise Rejection',
        showToast: false // Don't show toast for unhandled rejections
      });
    });

    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      
      const error = new AppError(
        event.error?.message || 'An unexpected error occurred',
        'GLOBAL_ERROR'
      );
      
      displayError(error, { 
        context: 'Global Error Handler',
        showToast: false // Don't show toast for global errors
      });
    });
  }
}
