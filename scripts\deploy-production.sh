#!/bin/bash

# AI Development Stack GUI - Production Deployment Script
# This script automates the complete production deployment process
# Version: 1.0.0

set -euo pipefail

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_FILE="${PROJECT_ROOT}/deployment.log"
readonly BACKUP_DIR="${PROJECT_ROOT}/backups/$(date +%Y%m%d_%H%M%S)"
readonly DEPLOYMENT_MODE="${1:-production}"
readonly FORCE_REINSTALL="${2:-false}"

# System requirements
readonly MIN_DOCKER_VERSION="20.10.0"
readonly MIN_COMPOSE_VERSION="2.0.0"
readonly MIN_NODE_VERSION="18.0.0"
readonly MIN_MEMORY_GB=4
readonly MIN_DISK_GB=10

# Service configuration
readonly POSTGRES_VERSION="15"
readonly REDIS_VERSION="7"
readonly NGINX_VERSION="1.24"

# Network and security settings
readonly NETWORK_NAME="ai-stack-production"
readonly SSL_CERT_PATH="/etc/ssl/certs/ai-stack"
readonly BACKUP_RETENTION_DAYS=30

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "DEBUG") echo -e "${PURPLE}[DEBUG]${NC} $message" ;;
        "STEP") echo -e "${CYAN}[STEP]${NC} $message" ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    log "ERROR" "Deployment failed. Check $LOG_FILE for details."
    cleanup_on_failure
    exit 1
}

# Cleanup function for failures
cleanup_on_failure() {
    log "WARNING" "Cleaning up failed deployment..."
    
    # Stop any running containers
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" down 2>/dev/null || true
    
    # Remove any created networks
    docker network rm "$NETWORK_NAME" 2>/dev/null || true
    
    log "WARNING" "Cleanup completed"
}

# Trap for error handling
trap 'error_exit "Script interrupted"' INT TERM
trap 'error_exit "An error occurred on line $LINENO"' ERR

# Initialize logging
init_logging() {
    mkdir -p "$(dirname "$LOG_FILE")"
    echo "=== AI Development Stack GUI Production Deployment ===" > "$LOG_FILE"
    echo "Started at: $(date)" >> "$LOG_FILE"
    echo "Deployment mode: $DEPLOYMENT_MODE" >> "$LOG_FILE"
    echo "Project root: $PROJECT_ROOT" >> "$LOG_FILE"
    echo "========================================================" >> "$LOG_FILE"
    
    log "INFO" "Deployment started in $DEPLOYMENT_MODE mode"
    log "INFO" "Logs will be written to: $LOG_FILE"
}

# Check if running as root (not recommended for production)
check_user_privileges() {
    log "STEP" "Checking user privileges..."
    
    if [[ $EUID -eq 0 ]]; then
        log "WARNING" "Running as root is not recommended for production deployment"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            error_exit "Deployment cancelled by user"
        fi
    fi
    
    # Check if user is in docker group
    if ! groups | grep -q docker; then
        log "WARNING" "User is not in docker group. You may need to use sudo for Docker commands"
    fi
    
    log "SUCCESS" "User privilege check completed"
}

# System requirements check
check_system_requirements() {
    log "STEP" "Checking system requirements..."
    
    # Check operating system
    local os_type=$(uname -s)
    log "INFO" "Operating system: $os_type"
    
    case "$os_type" in
        "Linux"|"Darwin")
            log "SUCCESS" "Supported operating system detected"
            ;;
        "MINGW"*|"CYGWIN"*|"MSYS"*)
            log "INFO" "Windows environment detected (Git Bash/WSL)"
            ;;
        *)
            error_exit "Unsupported operating system: $os_type"
            ;;
    esac
    
    # Check available memory
    if command -v free >/dev/null 2>&1; then
        local memory_gb=$(free -g | awk 'NR==2{print $2}')
        if [[ $memory_gb -lt $MIN_MEMORY_GB ]]; then
            log "WARNING" "Available memory ($memory_gb GB) is below recommended minimum ($MIN_MEMORY_GB GB)"
        else
            log "SUCCESS" "Memory check passed: $memory_gb GB available"
        fi
    fi
    
    # Check available disk space
    local disk_space_gb=$(df "$PROJECT_ROOT" | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $disk_space_gb -lt $MIN_DISK_GB ]]; then
        error_exit "Insufficient disk space. Required: $MIN_DISK_GB GB, Available: $disk_space_gb GB"
    fi
    
    log "SUCCESS" "Disk space check passed: $disk_space_gb GB available"
    log "SUCCESS" "System requirements check completed"
}

# Version comparison function
version_compare() {
    local version1="$1"
    local version2="$2"
    
    if [[ "$(printf '%s\n' "$version1" "$version2" | sort -V | head -n1)" == "$version2" ]]; then
        return 0  # version1 >= version2
    else
        return 1  # version1 < version2
    fi
}

# Check and install Docker
check_install_docker() {
    log "STEP" "Checking Docker installation..."
    
    if command -v docker >/dev/null 2>&1; then
        local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log "INFO" "Docker version found: $docker_version"
        
        if version_compare "$docker_version" "$MIN_DOCKER_VERSION"; then
            log "SUCCESS" "Docker version is compatible"
        else
            error_exit "Docker version $docker_version is below minimum required version $MIN_DOCKER_VERSION"
        fi
        
        # Check if Docker daemon is running
        if ! docker info >/dev/null 2>&1; then
            error_exit "Docker daemon is not running. Please start Docker and try again."
        fi
        
        log "SUCCESS" "Docker is running and accessible"
    else
        log "WARNING" "Docker not found. Installing Docker..."
        install_docker
    fi
}

# Install Docker (Linux only)
install_docker() {
    local os_type=$(uname -s)
    
    case "$os_type" in
        "Linux")
            log "INFO" "Installing Docker on Linux..."
            
            # Update package index
            sudo apt-get update
            
            # Install prerequisites
            sudo apt-get install -y \
                apt-transport-https \
                ca-certificates \
                curl \
                gnupg \
                lsb-release
            
            # Add Docker's official GPG key
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            
            # Set up stable repository
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # Install Docker Engine
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            
            # Add user to docker group
            sudo usermod -aG docker "$USER"
            
            log "SUCCESS" "Docker installed successfully"
            log "WARNING" "Please log out and log back in for group changes to take effect"
            ;;
        *)
            error_exit "Automatic Docker installation is only supported on Linux. Please install Docker manually from https://docs.docker.com/get-docker/"
            ;;
    esac
}

# Check Docker Compose
check_docker_compose() {
    log "STEP" "Checking Docker Compose..."
    
    if command -v docker-compose >/dev/null 2>&1; then
        local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log "INFO" "Docker Compose version found: $compose_version"
        
        if version_compare "$compose_version" "$MIN_COMPOSE_VERSION"; then
            log "SUCCESS" "Docker Compose version is compatible"
        else
            error_exit "Docker Compose version $compose_version is below minimum required version $MIN_COMPOSE_VERSION"
        fi
    elif docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker compose version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log "INFO" "Docker Compose (plugin) version found: $compose_version"
        
        if version_compare "$compose_version" "$MIN_COMPOSE_VERSION"; then
            log "SUCCESS" "Docker Compose version is compatible"
            # Create alias for docker-compose
            alias docker-compose='docker compose'
        else
            error_exit "Docker Compose version $compose_version is below minimum required version $MIN_COMPOSE_VERSION"
        fi
    else
        error_exit "Docker Compose not found. Please install Docker Compose."
    fi
}

# Check Node.js (for local development tools)
check_nodejs() {
    log "STEP" "Checking Node.js installation..."
    
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        log "INFO" "Node.js version found: $node_version"
        
        if version_compare "$node_version" "$MIN_NODE_VERSION"; then
            log "SUCCESS" "Node.js version is compatible"
        else
            log "WARNING" "Node.js version $node_version is below recommended version $MIN_NODE_VERSION"
        fi
    else
        log "WARNING" "Node.js not found. Some development tools may not work."
        log "INFO" "You can install Node.js from https://nodejs.org/"
    fi
}

# Generate secure random string
generate_secure_random() {
    local length="${1:-32}"
    openssl rand -base64 "$length" | tr -d "=+/" | cut -c1-"$length"
}

# Generate production environment files
generate_production_config() {
    log "STEP" "Generating production configuration..."
    
    # Create backup of existing configs
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        mkdir -p "$BACKUP_DIR"
        cp "$PROJECT_ROOT/.env" "$BACKUP_DIR/.env.backup"
        log "INFO" "Backed up existing .env to $BACKUP_DIR"
    fi
    
    # Generate secure secrets
    local jwt_secret=$(generate_secure_random 64)
    local postgres_password=$(generate_secure_random 32)
    local redis_password=$(generate_secure_random 32)
    local session_secret=$(generate_secure_random 32)
    
    # Get server IP for configuration
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "localhost")
    
    # Create root .env file
    cat > "$PROJECT_ROOT/.env" << EOF
# Production Environment Configuration
# Generated on: $(date)
# WARNING: Keep this file secure and never commit to version control

# Environment
NODE_ENV=production
DEPLOYMENT_MODE=production

# Database Configuration
POSTGRES_PASSWORD=$postgres_password
POSTGRES_DB=ai_dev_stack_prod
POSTGRES_USER=postgres

# Redis Configuration
REDIS_PASSWORD=$redis_password

# JWT Configuration
JWT_SECRET=$jwt_secret
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=$session_secret

# External Services (Update these with your actual service URLs)
OLLAMA_BASE_URL=http://host.docker.internal:11434
COMFYUI_BASE_URL=http://host.docker.internal:8188
AUGMENT_CODE_API_KEY=your_api_key_here

# Server Configuration
SERVER_IP=$server_ip
DOMAIN=localhost
SSL_ENABLED=false

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true

# Security
RATE_LIMIT_ENABLED=true
CORS_ENABLED=true
HELMET_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=$BACKUP_RETENTION_DAYS
EOF

    # Create backend production environment
    cat > "$PROJECT_ROOT/backend/.env.production" << EOF
# Backend Production Environment
NODE_ENV=production
PORT=3001

# Database
DATABASE_URL=******************************************************/ai_dev_stack_prod

# Redis
REDIS_URL=redis://:$redis_password@redis:6379

# JWT
JWT_SECRET=$jwt_secret
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# External Services
OLLAMA_BASE_URL=http://host.docker.internal:11434
COMFYUI_BASE_URL=http://host.docker.internal:8188
AUGMENT_CODE_API_KEY=your_api_key_here

# Security
CORS_ORIGIN=https://$server_ip,http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Performance
MAX_REQUEST_SIZE=10mb
COMPRESSION_ENABLED=true
EOF

    # Create frontend production environment
    cat > "$PROJECT_ROOT/frontend/.env.production" << EOF
# Frontend Production Environment
NODE_ENV=production

# API Configuration
NEXT_PUBLIC_API_URL=http://$server_ip:3001
NEXT_PUBLIC_WS_URL=ws://$server_ip:3001

# Application Configuration
NEXT_PUBLIC_APP_NAME=AI Development Stack GUI
NEXT_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true

# Performance
NEXT_PUBLIC_ENABLE_SW=true
NEXT_PUBLIC_ENABLE_COMPRESSION=true

# Security
NEXT_PUBLIC_CSP_ENABLED=true
EOF

    # Set secure file permissions
    chmod 600 "$PROJECT_ROOT/.env"
    chmod 600 "$PROJECT_ROOT/backend/.env.production"
    chmod 600 "$PROJECT_ROOT/frontend/.env.production"
    
    log "SUCCESS" "Production configuration generated"
    log "WARNING" "Please update external service URLs in the .env files"
    log "WARNING" "Store the generated passwords securely: $BACKUP_DIR/secrets.txt"
    
    # Save secrets for reference
    mkdir -p "$BACKUP_DIR"
    cat > "$BACKUP_DIR/secrets.txt" << EOF
AI Development Stack GUI - Production Secrets
Generated on: $(date)

PostgreSQL Password: $postgres_password
Redis Password: $redis_password
JWT Secret: $jwt_secret
Session Secret: $session_secret

IMPORTANT: Store these secrets securely and delete this file after noting them down.
EOF
    chmod 600 "$BACKUP_DIR/secrets.txt"
}

# Create production Docker Compose override
create_production_compose() {
    log "STEP" "Creating production Docker Compose configuration..."

    cat > "$PROJECT_ROOT/docker-compose.prod.yml" << EOF
version: '3.8'

# Production Docker Compose Configuration
services:
  # Production PostgreSQL with optimizations
  postgres:
    image: postgres:$POSTGRES_VERSION
    container_name: ai-stack-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ai_dev_stack_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    command: |
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c log_statement=all
      -c log_min_duration_statement=1000
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-stack-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Production Redis with persistence
  redis:
    image: redis:$REDIS_VERSION
    container_name: ai-stack-redis-prod
    restart: unless-stopped
    command: |
      redis-server
      --requirepass \${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_prod:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-stack-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Production Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: ai-stack-backend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
    env_file:
      - ./backend/.env.production
    volumes:
      - backend_logs_prod:/app/logs
      - backend_uploads_prod:/app/uploads
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - ai-stack-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Production Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: ai-stack-frontend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
    env_file:
      - ./frontend/.env.production
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - ai-stack-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:$NGINX_VERSION
    container_name: ai-stack-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - ai-stack-network
    profiles:
      - nginx
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  backend_logs_prod:
    driver: local
  backend_uploads_prod:
    driver: local
  nginx_logs_prod:
    driver: local

networks:
  ai-stack-network:
    driver: bridge
    name: $NETWORK_NAME
EOF

    log "SUCCESS" "Production Docker Compose configuration created"
}

# Create production Dockerfiles
create_production_dockerfiles() {
    log "STEP" "Creating production Dockerfiles..."

    # Backend production Dockerfile
    cat > "$PROJECT_ROOT/backend/Dockerfile" << 'EOF'
# Multi-stage build for production
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Development stage
FROM base AS development
RUN npm ci
COPY . .
RUN npx prisma generate
EXPOSE 3001
CMD ["npm", "run", "dev"]

# Build stage
FROM base AS build
RUN npm ci --only=production && npm cache clean --force
COPY . .
RUN npx prisma generate
RUN npm run build

# Production stage
FROM node:18-alpine AS production
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

WORKDIR /app

# Copy built application
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nodejs:nodejs /app/package*.json ./
COPY --from=build --chown=nodejs:nodejs /app/prisma ./prisma

# Create necessary directories
RUN mkdir -p logs uploads && chown -R nodejs:nodejs logs uploads

USER nodejs

EXPOSE 3001

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "dist/index.js"]
EOF

    # Frontend production Dockerfile
    cat > "$PROJECT_ROOT/frontend/Dockerfile" << 'EOF'
# Multi-stage build for production
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache libc6-compat git curl

WORKDIR /app

# Copy package files
COPY package*.json ./

# Development stage
FROM base AS development
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# Dependencies stage
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS build
RUN npm ci
COPY . .

# Build the application
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
RUN npm run build

# Production stage
FROM node:18-alpine AS production
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# Copy built application
COPY --from=build --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=build --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=build --chown=nextjs:nodejs /app/public ./public

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000 || exit 1

CMD ["node", "server.js"]
EOF

    log "SUCCESS" "Production Dockerfiles created"
}

# Create Nginx configuration
create_nginx_config() {
    log "STEP" "Creating Nginx configuration..."

    mkdir -p "$PROJECT_ROOT/nginx"

    cat > "$PROJECT_ROOT/nginx/nginx.prod.conf" << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream servers
    upstream backend {
        server backend:3001;
        keepalive 32;
    }

    upstream frontend {
        server frontend:3000;
        keepalive 32;
    }

    # Main server block
    server {
        listen 80;
        server_name _;

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # WebSocket routes
        location /socket.io/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

    log "SUCCESS" "Nginx configuration created"
}

# Setup database and run migrations
setup_database() {
    log "STEP" "Setting up production database..."

    # Start database services first
    log "INFO" "Starting database services..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" up -d postgres redis

    # Wait for database to be ready
    log "INFO" "Waiting for database to be ready..."
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
            log "SUCCESS" "Database is ready"
            break
        fi

        log "INFO" "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done

    if [[ $attempt -gt $max_attempts ]]; then
        error_exit "Database failed to start within expected time"
    fi

    # Wait for Redis to be ready
    log "INFO" "Waiting for Redis to be ready..."
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T redis redis-cli ping >/dev/null 2>&1; then
            log "SUCCESS" "Redis is ready"
            break
        fi

        log "INFO" "Waiting for Redis... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    if [[ $attempt -gt $max_attempts ]]; then
        error_exit "Redis failed to start within expected time"
    fi

    # Run database migrations
    log "INFO" "Running database migrations..."
    cd "$PROJECT_ROOT/backend"

    # Install dependencies if needed
    if [[ ! -d "node_modules" ]] || [[ "$FORCE_REINSTALL" == "true" ]]; then
        log "INFO" "Installing backend dependencies..."
        npm ci --only=production
    fi

    # Generate Prisma client
    npx prisma generate

    # Run migrations
    DATABASE_URL="postgresql://postgres:$(grep POSTGRES_PASSWORD "$PROJECT_ROOT/.env" | cut -d'=' -f2)@localhost:5432/ai_dev_stack_prod" \
    npx prisma migrate deploy

    cd "$PROJECT_ROOT"

    log "SUCCESS" "Database setup completed"
}

# Seed database with initial data
seed_database() {
    log "STEP" "Seeding database with initial data..."

    cd "$PROJECT_ROOT/backend"

    # Check if seed script exists
    if [[ -f "src/scripts/seed.ts" ]] || [[ -f "scripts/seed.js" ]]; then
        log "INFO" "Running database seed script..."
        DATABASE_URL="postgresql://postgres:$(grep POSTGRES_PASSWORD "$PROJECT_ROOT/.env" | cut -d'=' -f2)@localhost:5432/ai_dev_stack_prod" \
        npm run db:seed 2>/dev/null || {
            log "WARNING" "Database seeding failed or no seed script available"
        }
    else
        log "INFO" "No database seed script found, skipping seeding"
    fi

    cd "$PROJECT_ROOT"

    log "SUCCESS" "Database seeding completed"
}

# Build Docker images
build_docker_images() {
    log "STEP" "Building Docker images for production..."

    # Build backend image
    log "INFO" "Building backend image..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" build backend

    # Build frontend image
    log "INFO" "Building frontend image..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" build frontend

    # Tag images with version
    local version=$(date +%Y%m%d_%H%M%S)
    docker tag ai-development-stack-gui_backend:latest ai-development-stack-gui_backend:$version
    docker tag ai-development-stack-gui_frontend:latest ai-development-stack-gui_frontend:$version

    log "SUCCESS" "Docker images built successfully"
    log "INFO" "Images tagged with version: $version"
}

# Start all services
start_services() {
    log "STEP" "Starting all production services..."

    # Create Docker network if it doesn't exist
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        log "INFO" "Creating Docker network: $NETWORK_NAME"
        docker network create "$NETWORK_NAME"
    fi

    # Start all services
    log "INFO" "Starting all services with Docker Compose..."
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" up -d

    # Wait for services to start
    log "INFO" "Waiting for services to start..."
    sleep 30

    log "SUCCESS" "All services started"
}

# Comprehensive health checks
perform_health_checks() {
    log "STEP" "Performing comprehensive health checks..."

    local max_attempts=20
    local attempt=1
    local all_healthy=false

    while [[ $attempt -le $max_attempts ]] && [[ "$all_healthy" == "false" ]]; do
        log "INFO" "Health check attempt $attempt/$max_attempts"

        local postgres_healthy=false
        local redis_healthy=false
        local backend_healthy=false
        local frontend_healthy=false

        # Check PostgreSQL
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
            postgres_healthy=true
            log "SUCCESS" "✓ PostgreSQL is healthy"
        else
            log "WARNING" "✗ PostgreSQL is not ready"
        fi

        # Check Redis
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T redis redis-cli ping >/dev/null 2>&1; then
            redis_healthy=true
            log "SUCCESS" "✓ Redis is healthy"
        else
            log "WARNING" "✗ Redis is not ready"
        fi

        # Check Backend API
        if curl -f -s http://localhost:3001/health >/dev/null 2>&1; then
            backend_healthy=true
            log "SUCCESS" "✓ Backend API is healthy"
        else
            log "WARNING" "✗ Backend API is not ready"
        fi

        # Check Frontend
        if curl -f -s http://localhost:3000 >/dev/null 2>&1; then
            frontend_healthy=true
            log "SUCCESS" "✓ Frontend is healthy"
        else
            log "WARNING" "✗ Frontend is not ready"
        fi

        # Check if all services are healthy
        if [[ "$postgres_healthy" == "true" ]] && [[ "$redis_healthy" == "true" ]] && \
           [[ "$backend_healthy" == "true" ]] && [[ "$frontend_healthy" == "true" ]]; then
            all_healthy=true
            log "SUCCESS" "All services are healthy!"
            break
        fi

        if [[ $attempt -lt $max_attempts ]]; then
            log "INFO" "Waiting 15 seconds before next health check..."
            sleep 15
        fi

        ((attempt++))
    done

    if [[ "$all_healthy" == "false" ]]; then
        log "ERROR" "Health checks failed after $max_attempts attempts"
        log "INFO" "Checking service logs for issues..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" logs --tail=50
        error_exit "One or more services failed health checks"
    fi

    log "SUCCESS" "All health checks passed"
}

# Apply security hardening
apply_security_hardening() {
    log "STEP" "Applying security hardening..."

    # Set secure file permissions
    log "INFO" "Setting secure file permissions..."
    find "$PROJECT_ROOT" -name "*.env*" -exec chmod 600 {} \;
    find "$PROJECT_ROOT" -name "docker-compose*.yml" -exec chmod 644 {} \;
    find "$PROJECT_ROOT/scripts" -name "*.sh" -exec chmod 755 {} \;

    # Create security directories
    mkdir -p "$PROJECT_ROOT/security/ssl"
    mkdir -p "$PROJECT_ROOT/security/keys"
    chmod 700 "$PROJECT_ROOT/security"

    # Generate SSL certificates if not exists (self-signed for development)
    if [[ ! -f "$PROJECT_ROOT/security/ssl/server.crt" ]]; then
        log "INFO" "Generating self-signed SSL certificates..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$PROJECT_ROOT/security/ssl/server.key" \
            -out "$PROJECT_ROOT/security/ssl/server.crt" \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost" \
            2>/dev/null
        chmod 600 "$PROJECT_ROOT/security/ssl/server.key"
        chmod 644 "$PROJECT_ROOT/security/ssl/server.crt"
        log "WARNING" "Self-signed certificates generated. Replace with proper certificates for production."
    fi

    # Create security configuration
    cat > "$PROJECT_ROOT/security/security.conf" << EOF
# Security Configuration
# Generated on: $(date)

# File Permissions
umask 027

# Network Security
net.ipv4.tcp_syncookies = 1
net.ipv4.ip_forward = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.all.accept_source_route = 0

# Docker Security
DOCKER_CONTENT_TRUST=1
DOCKER_CONTENT_TRUST_SERVER=https://notary.docker.io
EOF

    # Set up log rotation
    if command -v logrotate >/dev/null 2>&1; then
        log "INFO" "Setting up log rotation..."
        cat > "$PROJECT_ROOT/security/logrotate.conf" << EOF
$PROJECT_ROOT/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f $PROJECT_ROOT/docker-compose.prod.yml restart backend frontend 2>/dev/null || true
    endscript
}
EOF
    fi

    log "SUCCESS" "Security hardening applied"
}

# Setup monitoring and logging
setup_monitoring() {
    log "STEP" "Setting up monitoring and logging..."

    # Create monitoring directories
    mkdir -p "$PROJECT_ROOT/monitoring/logs"
    mkdir -p "$PROJECT_ROOT/monitoring/metrics"
    mkdir -p "$PROJECT_ROOT/monitoring/alerts"

    # Create monitoring configuration
    cat > "$PROJECT_ROOT/monitoring/monitoring.yml" << EOF
# Monitoring Configuration
version: '3.8'

services:
  # Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-stack-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-stack-network
    profiles:
      - monitoring

  # Grafana (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: ai-stack-grafana
    restart: unless-stopped
    ports:
      - "3003:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - ai-stack-network
    profiles:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  ai-stack-network:
    external: true
EOF

    # Create basic Prometheus configuration
    cat > "$PROJECT_ROOT/monitoring/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'ai-stack-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'

  - job_name: 'ai-stack-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/metrics'
EOF

    # Create log aggregation script
    cat > "$PROJECT_ROOT/monitoring/collect-logs.sh" << 'EOF'
#!/bin/bash
# Log collection script

LOG_DIR="/app/monitoring/logs"
DATE=$(date +%Y%m%d)

# Collect Docker logs
docker-compose -f /app/docker-compose.prod.yml logs --no-color > "$LOG_DIR/docker-$DATE.log"

# Collect system logs
journalctl --since "1 day ago" > "$LOG_DIR/system-$DATE.log" 2>/dev/null || true

# Compress old logs
find "$LOG_DIR" -name "*.log" -mtime +7 -exec gzip {} \;

# Remove old compressed logs
find "$LOG_DIR" -name "*.log.gz" -mtime +30 -delete
EOF

    chmod +x "$PROJECT_ROOT/monitoring/collect-logs.sh"

    # Create health monitoring script
    cat > "$PROJECT_ROOT/monitoring/health-monitor.sh" << 'EOF'
#!/bin/bash
# Health monitoring script

ALERT_EMAIL="${ALERT_EMAIL:-admin@localhost}"
LOG_FILE="/app/monitoring/logs/health-$(date +%Y%m%d).log"

check_service() {
    local service="$1"
    local url="$2"
    local name="$3"

    if curl -f -s "$url" >/dev/null 2>&1; then
        echo "$(date): $name is healthy" >> "$LOG_FILE"
        return 0
    else
        echo "$(date): $name is DOWN" >> "$LOG_FILE"
        echo "ALERT: $name service is down at $url" | mail -s "Service Alert: $name Down" "$ALERT_EMAIL" 2>/dev/null || true
        return 1
    fi
}

# Check all services
check_service "frontend" "http://localhost:3000" "Frontend"
check_service "backend" "http://localhost:3001/health" "Backend API"

# Check Docker containers
if ! docker-compose -f /app/docker-compose.prod.yml ps | grep -q "Up"; then
    echo "$(date): Some Docker containers are not running" >> "$LOG_FILE"
    echo "ALERT: Docker containers status issue" | mail -s "Service Alert: Container Issue" "$ALERT_EMAIL" 2>/dev/null || true
fi
EOF

    chmod +x "$PROJECT_ROOT/monitoring/health-monitor.sh"

    log "SUCCESS" "Monitoring and logging setup completed"
    log "INFO" "To enable monitoring stack, run: docker-compose -f monitoring/monitoring.yml --profile monitoring up -d"
}

# Create backup system
setup_backup_system() {
    log "STEP" "Setting up backup system..."

    mkdir -p "$PROJECT_ROOT/backups/database"
    mkdir -p "$PROJECT_ROOT/backups/files"

    # Create database backup script
    cat > "$PROJECT_ROOT/backups/backup-database.sh" << 'EOF'
#!/bin/bash
# Database backup script

BACKUP_DIR="/app/backups/database"
DATE=$(date +%Y%m%d_%H%M%S)
POSTGRES_PASSWORD=$(grep POSTGRES_PASSWORD /app/.env | cut -d'=' -f2)

# Create database backup
docker-compose -f /app/docker-compose.prod.yml exec -T postgres pg_dump \
    -U postgres -d ai_dev_stack_prod | gzip > "$BACKUP_DIR/backup_$DATE.sql.gz"

# Remove old backups (keep last 30 days)
find "$BACKUP_DIR" -name "backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: backup_$DATE.sql.gz"
EOF

    chmod +x "$PROJECT_ROOT/backups/backup-database.sh"

    # Create file backup script
    cat > "$PROJECT_ROOT/backups/backup-files.sh" << 'EOF'
#!/bin/bash
# File backup script

BACKUP_DIR="/app/backups/files"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup uploaded files and logs
tar -czf "$BACKUP_DIR/files_$DATE.tar.gz" \
    /app/backend/uploads \
    /app/monitoring/logs \
    2>/dev/null || true

# Remove old file backups (keep last 7 days)
find "$BACKUP_DIR" -name "files_*.tar.gz" -mtime +7 -delete

echo "File backup completed: files_$DATE.tar.gz"
EOF

    chmod +x "$PROJECT_ROOT/backups/backup-files.sh"

    log "SUCCESS" "Backup system setup completed"
}

# Display deployment summary
display_deployment_summary() {
    log "STEP" "Deployment Summary"

    echo
    echo "=========================================="
    echo "  AI Development Stack GUI - DEPLOYED"
    echo "=========================================="
    echo
    echo "🌐 Application URLs:"
    echo "   Frontend:  http://localhost:3000"
    echo "   Backend:   http://localhost:3001"
    echo "   API Docs:  http://localhost:3001/api-docs"
    echo
    echo "🔧 Admin Tools:"
    echo "   Database:  localhost:5432"
    echo "   Redis:     localhost:6379"
    echo
    echo "📊 Optional Monitoring (if enabled):"
    echo "   Prometheus: http://localhost:9090"
    echo "   Grafana:    http://localhost:3003 (admin/admin)"
    echo
    echo "🔐 Default Login Credentials:"
    echo "   Admin: <EMAIL> / admin123!@#"
    echo "   Demo:  <EMAIL> / demo123!@#"
    echo
    echo "📁 Important Directories:"
    echo "   Logs:      $PROJECT_ROOT/monitoring/logs"
    echo "   Backups:   $PROJECT_ROOT/backups"
    echo "   Security:  $PROJECT_ROOT/security"
    echo
    echo "🛠️  Management Commands:"
    echo "   Status:    docker-compose -f docker-compose.prod.yml ps"
    echo "   Logs:      docker-compose -f docker-compose.prod.yml logs -f"
    echo "   Stop:      docker-compose -f docker-compose.prod.yml down"
    echo "   Restart:   docker-compose -f docker-compose.prod.yml restart"
    echo
    echo "📋 Next Steps:"
    echo "   1. Update external service URLs in .env files"
    echo "   2. Replace self-signed SSL certificates with proper ones"
    echo "   3. Configure monitoring alerts and notifications"
    echo "   4. Set up automated backups with cron jobs"
    echo "   5. Review and customize security settings"
    echo
    echo "📖 Documentation: $PROJECT_ROOT/docs/"
    echo "🔍 Troubleshooting: $PROJECT_ROOT/docs/TROUBLESHOOTING.md"
    echo
    echo "=========================================="

    log "SUCCESS" "Deployment completed successfully!"
    log "INFO" "Full deployment log available at: $LOG_FILE"
}

# Show usage information
show_usage() {
    cat << EOF
AI Development Stack GUI - Production Deployment Script

USAGE:
    $0 [MODE] [OPTIONS]

MODES:
    production     Full production deployment (default)
    staging        Staging environment deployment
    development    Development environment setup
    update         Update existing deployment
    rollback       Rollback to previous version

OPTIONS:
    --force-reinstall    Force reinstallation of all dependencies
    --skip-health-check  Skip health checks after deployment
    --enable-monitoring  Enable monitoring stack (Prometheus/Grafana)
    --enable-ssl         Enable SSL/HTTPS configuration
    --backup-first       Create backup before deployment
    --help              Show this help message

EXAMPLES:
    $0                                    # Full production deployment
    $0 production --enable-monitoring     # Production with monitoring
    $0 staging --force-reinstall          # Staging with fresh install
    $0 update --backup-first              # Update with backup

ENVIRONMENT VARIABLES:
    ALERT_EMAIL          Email for alerts (default: admin@localhost)
    DOMAIN              Domain name for SSL (default: localhost)
    SSL_CERT_PATH       Path to SSL certificates
    BACKUP_RETENTION    Backup retention days (default: 30)

For more information, see: $PROJECT_ROOT/docs/DEPLOYMENT.md
EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            production|staging|development|update|rollback)
                DEPLOYMENT_MODE="$1"
                shift
                ;;
            --force-reinstall)
                FORCE_REINSTALL="true"
                shift
                ;;
            --skip-health-check)
                SKIP_HEALTH_CHECK="true"
                shift
                ;;
            --enable-monitoring)
                ENABLE_MONITORING="true"
                shift
                ;;
            --enable-ssl)
                ENABLE_SSL="true"
                shift
                ;;
            --backup-first)
                BACKUP_FIRST="true"
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Pre-deployment backup
create_pre_deployment_backup() {
    if [[ "$BACKUP_FIRST" == "true" ]] || [[ "$DEPLOYMENT_MODE" == "update" ]]; then
        log "STEP" "Creating pre-deployment backup..."

        mkdir -p "$BACKUP_DIR"

        # Backup current environment files
        if [[ -f "$PROJECT_ROOT/.env" ]]; then
            cp "$PROJECT_ROOT/.env" "$BACKUP_DIR/.env.backup"
        fi

        # Backup database if running
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" ps postgres | grep -q "Up"; then
            log "INFO" "Backing up database..."
            "$PROJECT_ROOT/backups/backup-database.sh" || log "WARNING" "Database backup failed"
        fi

        # Backup uploaded files
        if [[ -d "$PROJECT_ROOT/backend/uploads" ]]; then
            tar -czf "$BACKUP_DIR/uploads.tar.gz" "$PROJECT_ROOT/backend/uploads" 2>/dev/null || true
        fi

        log "SUCCESS" "Pre-deployment backup created in $BACKUP_DIR"
    fi
}

# Main deployment function
main() {
    # Initialize
    init_logging

    log "INFO" "Starting AI Development Stack GUI production deployment"
    log "INFO" "Mode: $DEPLOYMENT_MODE"
    log "INFO" "Force reinstall: $FORCE_REINSTALL"

    # Parse arguments
    parse_arguments "$@"

    # Pre-flight checks
    check_user_privileges
    check_system_requirements

    # Install dependencies
    check_install_docker
    check_docker_compose
    check_nodejs

    # Pre-deployment backup
    create_pre_deployment_backup

    # Configuration
    generate_production_config
    create_production_compose
    create_production_dockerfiles
    create_nginx_config

    # Database setup
    setup_database

    # Seed database (optional)
    if [[ "$DEPLOYMENT_MODE" != "update" ]]; then
        seed_database
    fi

    # Build and deploy
    build_docker_images
    start_services

    # Health checks
    if [[ "$SKIP_HEALTH_CHECK" != "true" ]]; then
        perform_health_checks
    fi

    # Security and monitoring
    apply_security_hardening
    setup_monitoring
    setup_backup_system

    # Enable monitoring if requested
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        log "INFO" "Enabling monitoring stack..."
        docker-compose -f "$PROJECT_ROOT/monitoring/monitoring.yml" --profile monitoring up -d
    fi

    # Final summary
    display_deployment_summary

    log "SUCCESS" "Deployment completed successfully!"

    # Create success marker
    echo "$(date): Deployment completed successfully" > "$PROJECT_ROOT/.deployment-success"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Check if help is requested
    if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi

    # Run main function with all arguments
    main "$@"
fi
