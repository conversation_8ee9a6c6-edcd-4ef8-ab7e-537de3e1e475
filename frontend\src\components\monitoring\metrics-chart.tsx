'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  RefreshCw,
  Download
} from 'lucide-react';
import { useSystemWebSocket } from '@/hooks/useWebSocket';

interface MetricDataPoint {
  timestamp: number;
  value: number;
}

interface MetricsChartProps {
  title: string;
  description?: string;
  metricKey: string;
  unit?: string;
  color?: string;
  maxDataPoints?: number;
  refreshInterval?: number;
  className?: string;
}

export function MetricsChart({
  title,
  description,
  metricKey,
  unit = '%',
  color = '#3b82f6',
  maxDataPoints = 50,
  refreshInterval = 5000,
  className
}: MetricsChartProps) {
  const [data, setData] = useState<MetricDataPoint[]>([]);
  const [timeRange, setTimeRange] = useState('5m');
  const [isLive, setIsLive] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { onMetricsUpdate, subscribeToMetrics } = useSystemWebSocket();

  // Subscribe to real-time metrics
  useEffect(() => {
    if (isLive) {
      subscribeToMetrics();
      
      const unsubscribe = onMetricsUpdate((metricsData) => {
        const value = getNestedValue(metricsData.metrics, metricKey);
        if (typeof value === 'number') {
          const newDataPoint: MetricDataPoint = {
            timestamp: Date.now(),
            value,
          };
          
          setData(prev => {
            const updated = [...prev, newDataPoint];
            return updated.slice(-maxDataPoints);
          });
        }
      });

      return unsubscribe;
    }
  }, [isLive, metricKey, maxDataPoints, onMetricsUpdate, subscribeToMetrics]);

  // Draw chart on canvas
  useEffect(() => {
    if (!canvasRef.current || data.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;
    const padding = 20;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Calculate scales
    const maxValue = Math.max(...data.map(d => d.value), 100);
    const minValue = Math.min(...data.map(d => d.value), 0);
    const valueRange = maxValue - minValue || 1;

    const xScale = (width - 2 * padding) / (data.length - 1 || 1);
    const yScale = (height - 2 * padding) / valueRange;

    // Draw grid lines
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (i * (height - 2 * padding)) / 5;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = padding + (i * (width - 2 * padding)) / 10;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    // Draw area under curve
    if (data.length > 1) {
      ctx.fillStyle = color + '20';
      ctx.beginPath();
      ctx.moveTo(padding, height - padding);
      
      data.forEach((point, index) => {
        const x = padding + index * xScale;
        const y = height - padding - (point.value - minValue) * yScale;
        if (index === 0) {
          ctx.lineTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.lineTo(padding + (data.length - 1) * xScale, height - padding);
      ctx.closePath();
      ctx.fill();
    }

    // Draw line
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((point, index) => {
      const x = padding + index * xScale;
      const y = height - padding - (point.value - minValue) * yScale;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();

    // Draw data points
    ctx.fillStyle = color;
    data.forEach((point, index) => {
      const x = padding + index * xScale;
      const y = height - padding - (point.value - minValue) * yScale;
      
      ctx.beginPath();
      ctx.arc(x, y, 2, 0, 2 * Math.PI);
      ctx.fill();
    });

  }, [data, color]);

  const getCurrentValue = () => {
    return data.length > 0 ? data[data.length - 1].value : 0;
  };

  const getTrend = () => {
    if (data.length < 2) return 'stable';
    
    const current = data[data.length - 1].value;
    const previous = data[data.length - 2].value;
    const diff = current - previous;
    
    if (Math.abs(diff) < 0.1) return 'stable';
    return diff > 0 ? 'up' : 'down';
  };

  const getTrendIcon = () => {
    const trend = getTrend();
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const exportData = () => {
    const csv = [
      'Timestamp,Value',
      ...data.map(point => `${new Date(point.timestamp).toISOString()},${point.value}`)
    ].join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${metricKey}-metrics.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {title}
              {getTrendIcon()}
            </CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isLive ? 'default' : 'secondary'}>
              {isLive ? 'Live' : 'Paused'}
            </Badge>
            <div className="text-2xl font-bold">
              {getCurrentValue().toFixed(1)}{unit}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Chart */}
          <div className="relative h-48 w-full">
            <canvas
              ref={canvasRef}
              className="absolute inset-0 w-full h-full"
              style={{ width: '100%', height: '100%' }}
            />
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1m">1m</SelectItem>
                  <SelectItem value="5m">5m</SelectItem>
                  <SelectItem value="15m">15m</SelectItem>
                  <SelectItem value="1h">1h</SelectItem>
                  <SelectItem value="6h">6h</SelectItem>
                  <SelectItem value="24h">24h</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsLive(!isLive)}
              >
                {isLive ? 'Pause' : 'Resume'}
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportData}
                disabled={data.length === 0}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setData([])}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="font-medium">
                {data.length > 0 ? Math.max(...data.map(d => d.value)).toFixed(1) : '0'}{unit}
              </div>
              <div className="text-muted-foreground">Max</div>
            </div>
            <div className="text-center">
              <div className="font-medium">
                {data.length > 0 ? (data.reduce((sum, d) => sum + d.value, 0) / data.length).toFixed(1) : '0'}{unit}
              </div>
              <div className="text-muted-foreground">Avg</div>
            </div>
            <div className="text-center">
              <div className="font-medium">
                {data.length > 0 ? Math.min(...data.map(d => d.value)).toFixed(1) : '0'}{unit}
              </div>
              <div className="text-muted-foreground">Min</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}
