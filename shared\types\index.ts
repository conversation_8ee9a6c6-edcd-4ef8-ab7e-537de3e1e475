// User and Authentication Types
export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

// Ollama Types
export interface OllamaModel {
  name: string;
  size: number;
  digest: string;
  modified_at: string;
  details?: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface OllamaModelInfo {
  modelfile: string;
  parameters: string;
  template: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  model?: string;
  conversationId: string;
}

export interface Conversation {
  id: string;
  title: string;
  model: string;
  userId: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatRequest {
  message: string;
  model: string;
  conversationId?: string;
  stream?: boolean;
}

// ComfyUI Types
export interface ComfyUINode {
  id: string;
  type: string;
  pos: [number, number];
  size: [number, number];
  flags: Record<string, any>;
  order: number;
  mode: number;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  properties: Record<string, any>;
}

export interface ComfyUIWorkflow {
  id: string;
  name: string;
  description?: string;
  nodes: Record<string, ComfyUINode>;
  links: Array<[number, number, number, number, number, string]>;
  groups: any[];
  config: Record<string, any>;
  version: string;
  userId: string;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ComfyUIQueueItem {
  id: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  results?: string[];
  userId: string;
}

// Augment Code Types
export interface CodeProject {
  id: string;
  name: string;
  path: string;
  language: string;
  framework?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CodeFile {
  id: string;
  projectId: string;
  path: string;
  name: string;
  content: string;
  language: string;
  size: number;
  lastModified: Date;
}

export interface CodeSuggestion {
  id: string;
  type: 'completion' | 'refactor' | 'fix' | 'optimize';
  title: string;
  description: string;
  code: string;
  startLine: number;
  endLine: number;
  confidence: number;
}

// System Status Types
export interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'error';
  url: string;
  lastCheck: Date;
  responseTime?: number;
  error?: string;
}

export interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    upload: number;
    download: number;
  };
  timestamp: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// WebSocket Event Types
export interface WebSocketEvent {
  type: string;
  payload: any;
  timestamp: Date;
}

export interface ChatStreamEvent extends WebSocketEvent {
  type: 'chat_stream';
  payload: {
    conversationId: string;
    content: string;
    done: boolean;
  };
}

export interface QueueUpdateEvent extends WebSocketEvent {
  type: 'queue_update';
  payload: {
    queueItem: ComfyUIQueueItem;
  };
}

export interface SystemStatusEvent extends WebSocketEvent {
  type: 'system_status';
  payload: {
    services: ServiceStatus[];
    metrics: SystemMetrics;
  };
}
