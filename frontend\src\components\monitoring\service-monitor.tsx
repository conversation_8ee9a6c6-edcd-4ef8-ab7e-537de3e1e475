'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Server, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock,
  RefreshCw,
  ExternalLink,
  Activity,
  Zap,
  Wifi
} from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';
import { systemApi } from '@/lib/api';
import { useSystemWebSocket } from '@/hooks/useWebSocket';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'error' | 'warning';
  url: string;
  lastCheck: string;
  responseTime: number;
  uptime?: number;
  version?: string;
  description?: string;
}

interface ServiceMonitorProps {
  className?: string;
}

export function ServiceMonitor({ className }: ServiceMonitorProps) {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const { onSystemStatus } = useSystemWebSocket();

  // Fetch system status
  const { data: statusData, isLoading, refetch } = useQuery({
    queryKey: ['system', 'status'],
    queryFn: systemApi.getStatus,
    refetchInterval: 30000,
  });

  // Listen for real-time status updates
  useEffect(() => {
    const unsubscribe = onSystemStatus((data) => {
      setServices(data.services || []);
    });

    return unsubscribe;
  }, [onSystemStatus]);

  // Initialize services from API data
  useEffect(() => {
    if (statusData?.data?.services) {
      setServices(statusData.data.services);
    }
  }, [statusData]);

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'offline':
        return 'bg-red-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'online':
        return 'default' as const;
      case 'offline':
      case 'error':
        return 'destructive' as const;
      case 'warning':
        return 'secondary' as const;
      default:
        return 'outline' as const;
    }
  };

  const getResponseTimeColor = (responseTime: number) => {
    if (responseTime < 100) return 'text-green-600';
    if (responseTime < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getUptimeColor = (uptime: number) => {
    if (uptime >= 99) return 'text-green-600';
    if (uptime >= 95) return 'text-yellow-600';
    return 'text-red-600';
  };

  const overallStatus = services.length > 0 ? 
    services.every(s => s.status === 'online') ? 'healthy' :
    services.some(s => s.status === 'offline' || s.status === 'error') ? 'critical' :
    'warning' : 'unknown';

  const onlineServices = services.filter(s => s.status === 'online').length;
  const totalServices = services.length;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Service Monitor
              <Badge variant={
                overallStatus === 'healthy' ? 'default' :
                overallStatus === 'critical' ? 'destructive' : 'secondary'
              }>
                {overallStatus}
              </Badge>
            </CardTitle>
            <CardDescription>
              Real-time monitoring of integrated AI services
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right">
              <div className="text-2xl font-bold">
                {onlineServices}/{totalServices}
              </div>
              <div className="text-sm text-muted-foreground">Online</div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : services.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Server className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No services configured</p>
            <p className="text-sm">Configure external services to monitor their status</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Overall Health Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Overall Health</span>
                <span>{Math.round((onlineServices / totalServices) * 100)}%</span>
              </div>
              <Progress 
                value={(onlineServices / totalServices) * 100}
                className="h-2"
                indicatorClassName={
                  onlineServices === totalServices ? 'bg-green-500' :
                  onlineServices > totalServices / 2 ? 'bg-yellow-500' : 'bg-red-500'
                }
              />
            </div>

            {/* Services List */}
            <div className="space-y-3">
              {services.map((service) => (
                <div
                  key={service.name}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(service.status)}`} />
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{service.name}</h4>
                        {service.version && (
                          <Badge variant="outline" className="text-xs">
                            v{service.version}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {service.description || service.url}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    {/* Response Time */}
                    <div className="text-center">
                      <div className={`text-sm font-medium ${getResponseTimeColor(service.responseTime)}`}>
                        {service.responseTime}ms
                      </div>
                      <div className="text-xs text-muted-foreground">Response</div>
                    </div>

                    {/* Uptime */}
                    {service.uptime !== undefined && (
                      <div className="text-center">
                        <div className={`text-sm font-medium ${getUptimeColor(service.uptime)}`}>
                          {service.uptime.toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">Uptime</div>
                      </div>
                    )}

                    {/* Last Check */}
                    <div className="text-center">
                      <div className="text-sm font-medium">
                        {formatRelativeTime(service.lastCheck)}
                      </div>
                      <div className="text-xs text-muted-foreground">Last Check</div>
                    </div>

                    {/* Status & Actions */}
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(service.status)}>
                        {getStatusIcon(service.status)}
                        <span className="ml-1 capitalize">{service.status}</span>
                      </Badge>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(service.url, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Service Categories */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
                  <Activity className="h-4 w-4" />
                  <span className="font-medium">
                    {services.filter(s => s.name.toLowerCase().includes('ollama')).length}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">Ollama</div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-purple-600 mb-1">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">
                    {services.filter(s => s.name.toLowerCase().includes('comfy')).length}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">ComfyUI</div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
                  <Wifi className="h-4 w-4" />
                  <span className="font-medium">
                    {services.filter(s => s.name.toLowerCase().includes('augment')).length}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">Augment</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
