import request from 'supertest';
import { app } from '../app';
import { getPrismaClient } from '../utils/database';
import { OllamaService } from '../services/ollamaService';
import bcrypt from 'bcryptjs';

// Mock the OllamaService
jest.mock('../services/ollamaService');
const MockedOllamaService = OllamaService as jest.MockedClass<typeof OllamaService>;

const prisma = getPrismaClient();

describe('Ollama API', () => {
  let accessToken: string;
  let userId: string;

  beforeAll(async () => {
    // Create a test user and get access token
    const hashedPassword = await bcrypt.hash('Test123!@#', 12);
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
      },
    });

    userId = user.id;

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Test123!@#',
      });

    accessToken = loginResponse.body.data.tokens.accessToken;
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // Clean up
    await prisma.message.deleteMany();
    await prisma.conversation.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('GET /api/ollama/models', () => {
    it('should list models successfully', async () => {
      const mockModels = [
        {
          name: 'llama2:7b',
          size: 3825819519,
          digest: 'sha256:abc123',
          modified_at: '2024-01-01T00:00:00Z',
        },
        {
          name: 'codellama:13b',
          size: 7365960935,
          digest: 'sha256:def456',
          modified_at: '2024-01-01T00:00:00Z',
        },
      ];

      MockedOllamaService.prototype.listModels = jest.fn().mockResolvedValue({
        models: mockModels,
      });

      const response = await request(app)
        .get('/api/ollama/models')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.models).toHaveLength(2);
      expect(response.body.data.models[0].name).toBe('llama2:7b');
      expect(MockedOllamaService.prototype.listModels).toHaveBeenCalledTimes(1);
    });

    it('should handle service unavailable', async () => {
      MockedOllamaService.prototype.listModels = jest.fn().mockRejectedValue(
        new Error('Service unavailable')
      );

      const response = await request(app)
        .get('/api/ollama/models')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(503);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Service unavailable');
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/ollama/models')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/ollama/models/pull', () => {
    it('should pull model successfully', async () => {
      MockedOllamaService.prototype.pullModel = jest.fn().mockResolvedValue({
        status: 'success',
      });

      const response = await request(app)
        .post('/api/ollama/models/pull')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ name: 'llama2:7b' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(MockedOllamaService.prototype.pullModel).toHaveBeenCalledWith('llama2:7b');
    });

    it('should validate model name', async () => {
      const response = await request(app)
        .post('/api/ollama/models/pull')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({}) // Missing name
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle pull errors', async () => {
      MockedOllamaService.prototype.pullModel = jest.fn().mockRejectedValue(
        new Error('Model not found')
      );

      const response = await request(app)
        .post('/api/ollama/models/pull')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ name: 'nonexistent:model' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Model not found');
    });
  });

  describe('DELETE /api/ollama/models/:name', () => {
    it('should delete model successfully', async () => {
      MockedOllamaService.prototype.deleteModel = jest.fn().mockResolvedValue({
        status: 'success',
      });

      const response = await request(app)
        .delete('/api/ollama/models/llama2:7b')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(MockedOllamaService.prototype.deleteModel).toHaveBeenCalledWith('llama2:7b');
    });

    it('should handle delete errors', async () => {
      MockedOllamaService.prototype.deleteModel = jest.fn().mockRejectedValue(
        new Error('Model not found')
      );

      const response = await request(app)
        .delete('/api/ollama/models/nonexistent:model')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/ollama/conversations', () => {
    beforeEach(async () => {
      // Clean up conversations
      await prisma.message.deleteMany();
      await prisma.conversation.deleteMany();
    });

    it('should list conversations successfully', async () => {
      // Create test conversations
      const conversation1 = await prisma.conversation.create({
        data: {
          title: 'Test Conversation 1',
          model: 'llama2:7b',
          userId,
        },
      });

      const conversation2 = await prisma.conversation.create({
        data: {
          title: 'Test Conversation 2',
          model: 'codellama:13b',
          userId,
        },
      });

      const response = await request(app)
        .get('/api/ollama/conversations')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(2);
      expect(response.body.data.items[0].title).toBe('Test Conversation 2'); // Most recent first
      expect(response.body.data.items[1].title).toBe('Test Conversation 1');
    });

    it('should support pagination', async () => {
      // Create multiple conversations
      for (let i = 1; i <= 5; i++) {
        await prisma.conversation.create({
          data: {
            title: `Conversation ${i}`,
            model: 'llama2:7b',
            userId,
          },
        });
      }

      const response = await request(app)
        .get('/api/ollama/conversations?page=1&limit=3')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(3);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(3);
      expect(response.body.data.pagination.total).toBe(5);
    });

    it('should filter by search query', async () => {
      await prisma.conversation.create({
        data: {
          title: 'Python Programming Help',
          model: 'llama2:7b',
          userId,
        },
      });

      await prisma.conversation.create({
        data: {
          title: 'JavaScript Tutorial',
          model: 'llama2:7b',
          userId,
        },
      });

      const response = await request(app)
        .get('/api/ollama/conversations?search=Python')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toHaveLength(1);
      expect(response.body.data.items[0].title).toBe('Python Programming Help');
    });
  });

  describe('POST /api/ollama/conversations', () => {
    it('should create conversation successfully', async () => {
      const conversationData = {
        title: 'New Test Conversation',
        model: 'llama2:7b',
      };

      const response = await request(app)
        .post('/api/ollama/conversations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(conversationData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.conversation.title).toBe(conversationData.title);
      expect(response.body.data.conversation.model).toBe(conversationData.model);
      expect(response.body.data.conversation.userId).toBe(userId);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/ollama/conversations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({}) // Missing required fields
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/ollama/conversations/:id', () => {
    let conversationId: string;

    beforeEach(async () => {
      const conversation = await prisma.conversation.create({
        data: {
          title: 'Test Conversation',
          model: 'llama2:7b',
          userId,
        },
      });
      conversationId = conversation.id;

      // Add some messages
      await prisma.message.createMany({
        data: [
          {
            role: 'user',
            content: 'Hello, how are you?',
            conversationId,
          },
          {
            role: 'assistant',
            content: 'Hello! I am doing well, thank you for asking.',
            conversationId,
          },
        ],
      });
    });

    afterEach(async () => {
      await prisma.message.deleteMany();
      await prisma.conversation.deleteMany();
    });

    it('should get conversation with messages', async () => {
      const response = await request(app)
        .get(`/api/ollama/conversations/${conversationId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.conversation.id).toBe(conversationId);
      expect(response.body.data.conversation.messages).toHaveLength(2);
      expect(response.body.data.conversation.messages[0].role).toBe('user');
      expect(response.body.data.conversation.messages[1].role).toBe('assistant');
    });

    it('should return 404 for non-existent conversation', async () => {
      const response = await request(app)
        .get('/api/ollama/conversations/non-existent-id')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should not allow access to other users conversations', async () => {
      // Create another user
      const hashedPassword = await bcrypt.hash('Test123!@#', 12);
      const otherUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'otheruser',
          password: hashedPassword,
          firstName: 'Other',
          lastName: 'User',
        },
      });

      // Create conversation for other user
      const otherConversation = await prisma.conversation.create({
        data: {
          title: 'Other User Conversation',
          model: 'llama2:7b',
          userId: otherUser.id,
        },
      });

      const response = await request(app)
        .get(`/api/ollama/conversations/${otherConversation.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);

      // Clean up
      await prisma.conversation.delete({ where: { id: otherConversation.id } });
      await prisma.user.delete({ where: { id: otherUser.id } });
    });
  });

  describe('POST /api/ollama/chat', () => {
    let conversationId: string;

    beforeEach(async () => {
      const conversation = await prisma.conversation.create({
        data: {
          title: 'Test Chat',
          model: 'llama2:7b',
          userId,
        },
      });
      conversationId = conversation.id;
    });

    afterEach(async () => {
      await prisma.message.deleteMany();
      await prisma.conversation.deleteMany();
    });

    it('should send chat message successfully', async () => {
      const mockResponse = {
        message: {
          role: 'assistant',
          content: 'Hello! How can I help you today?',
        },
        done: true,
      };

      MockedOllamaService.prototype.chat = jest.fn().mockResolvedValue(mockResponse);

      const chatData = {
        message: 'Hello, how are you?',
        model: 'llama2:7b',
        conversationId,
      };

      const response = await request(app)
        .post('/api/ollama/chat')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(chatData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.response.message.content).toBe(mockResponse.message.content);

      // Verify messages were saved to database
      const messages = await prisma.message.findMany({
        where: { conversationId },
        orderBy: { createdAt: 'asc' },
      });

      expect(messages).toHaveLength(2);
      expect(messages[0].role).toBe('user');
      expect(messages[0].content).toBe(chatData.message);
      expect(messages[1].role).toBe('assistant');
      expect(messages[1].content).toBe(mockResponse.message.content);
    });

    it('should validate chat message', async () => {
      const response = await request(app)
        .post('/api/ollama/chat')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({}) // Missing required fields
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle chat errors', async () => {
      MockedOllamaService.prototype.chat = jest.fn().mockRejectedValue(
        new Error('Model not available')
      );

      const chatData = {
        message: 'Hello',
        model: 'nonexistent:model',
        conversationId,
      };

      const response = await request(app)
        .post('/api/ollama/chat')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(chatData)
        .expect(500);

      expect(response.body.success).toBe(false);
    });
  });
});
