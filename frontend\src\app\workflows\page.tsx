'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Plus, 
  Search, 
  Play, 
  Edit, 
  Trash2, 
  Loader2,
  Calendar,
  User,
  Eye,
  Copy
} from 'lucide-react';
import { formatDate, formatRelativeTime } from '@/lib/utils';
import { comfyuiApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function WorkflowsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch workflows
  const { data: workflowsData, isLoading, error } = useQuery({
    queryKey: ['comfyui', 'workflows'],
    queryFn: () => comfyuiApi.getWorkflows({ limit: 50 }),
  });

  // Execute workflow mutation
  const executeWorkflowMutation = useMutation({
    mutationFn: comfyuiApi.executeWorkflow,
    onSuccess: () => {
      toast.success('Workflow execution started');
      queryClient.invalidateQueries({ queryKey: ['comfyui', 'queue'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to execute workflow');
    },
  });

  // Delete workflow mutation
  const deleteWorkflowMutation = useMutation({
    mutationFn: comfyuiApi.deleteWorkflow,
    onSuccess: () => {
      toast.success('Workflow deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['comfyui', 'workflows'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete workflow');
    },
  });

  const workflows = workflowsData?.data?.items || [];
  const filteredWorkflows = workflows.filter((workflow: any) =>
    workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleExecuteWorkflow = (workflowId: string) => {
    executeWorkflowMutation.mutate({ workflowId });
  };

  const handleDeleteWorkflow = (workflowId: string, workflowName: string) => {
    if (confirm(`Are you sure you want to delete the workflow "${workflowName}"?`)) {
      deleteWorkflowMutation.mutate(workflowId);
    }
  };

  const handleCopyWorkflow = (workflow: any) => {
    // TODO: Implement workflow copying
    toast.info('Workflow copying coming soon');
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workflows</h1>
          <p className="text-muted-foreground">Manage your ComfyUI workflows</p>
        </div>
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            Failed to load workflows. Please check if ComfyUI is running and accessible.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workflows</h1>
          <p className="text-muted-foreground">
            Create and manage your ComfyUI image generation workflows ({workflows.length} workflows)
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Workflow
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Workflow</DialogTitle>
              <DialogDescription>
                Create a new ComfyUI workflow. You can start from scratch or import an existing workflow.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Workflow builder coming soon. For now, you can import workflows from ComfyUI.
              </p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => {
                  toast.info('Workflow builder coming soon');
                  setIsCreateDialogOpen(false);
                }}>
                  Create Workflow
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="relative w-full max-w-sm">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search workflows..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Workflows Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Loading workflows...</p>
          </div>
        </div>
      ) : filteredWorkflows.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {searchTerm ? 'No workflows found matching your search.' : 'No workflows created yet. Create your first workflow to get started.'}
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredWorkflows.map((workflow: any) => (
            <Card key={workflow.id} className="relative group">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg line-clamp-1">{workflow.name}</CardTitle>
                    {workflow.description && (
                      <CardDescription className="line-clamp-2">
                        {workflow.description}
                      </CardDescription>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    {workflow.isPublic && (
                      <Badge variant="secondary" className="text-xs">
                        <Eye className="w-3 h-3 mr-1" />
                        Public
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{formatRelativeTime(workflow.updatedAt)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>v{workflow.version}</span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleExecuteWorkflow(workflow.id)}
                    disabled={executeWorkflowMutation.isPending}
                    className="flex-1"
                  >
                    {executeWorkflowMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="mr-2 h-4 w-4" />
                    )}
                    Execute
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // TODO: Navigate to workflow editor
                      toast.info('Workflow editor coming soon');
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCopyWorkflow(workflow)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteWorkflow(workflow.id, workflow.name)}
                    disabled={deleteWorkflowMutation.isPending}
                    className="text-destructive hover:text-destructive"
                  >
                    {deleteWorkflowMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
