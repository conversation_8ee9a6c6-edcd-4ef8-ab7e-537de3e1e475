import { Router } from 'express';
import { authenticate, authorize } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/errorHandler';
import { checkDatabaseHealth } from '@/utils/database';
import { checkRedisHealth } from '@/utils/redis';
import { ApiResponse, ServiceStatus, SystemMetrics } from '@shared/types';

const router = Router();

// Get system status (public endpoint)
router.get('/status', asyncHandler(async (req, res) => {
  const [dbHealth, redisHealth] = await Promise.all([
    checkDatabaseHealth(),
    checkRedisHealth(),
  ]);

  const services: ServiceStatus[] = [
    {
      name: 'Database',
      status: dbHealth.status === 'healthy' ? 'online' : 'offline',
      url: 'postgresql://localhost:5432',
      lastCheck: new Date(),
      responseTime: dbHealth.responseTime,
      error: dbHealth.error,
    },
    {
      name: 'Redis',
      status: redisHealth.status === 'healthy' ? 'online' : 'offline',
      url: 'redis://localhost:6379',
      lastCheck: new Date(),
      responseTime: redisHealth.responseTime,
      error: redisHealth.error,
    },
    // TODO: Add external service health checks
    {
      name: 'Ollama',
      status: 'offline', // TODO: Implement health check
      url: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
      lastCheck: new Date(),
      error: 'Health check not implemented',
    },
    {
      name: 'ComfyUI',
      status: 'offline', // TODO: Implement health check
      url: process.env.COMFYUI_BASE_URL || 'http://localhost:8188',
      lastCheck: new Date(),
      error: 'Health check not implemented',
    },
  ];

  const overallStatus = services.every(s => s.status === 'online') ? 'healthy' : 'degraded';

  const response: ApiResponse<{ status: string; services: ServiceStatus[] }> = {
    success: true,
    data: {
      status: overallStatus,
      services,
    },
  };

  res.json(response);
}));

// Get system metrics (requires authentication)
router.get('/metrics', authenticate, asyncHandler(async (req, res) => {
  // TODO: Implement system metrics collection
  const metrics: SystemMetrics = {
    cpu: 0, // TODO: Get actual CPU usage
    memory: 0, // TODO: Get actual memory usage
    disk: 0, // TODO: Get actual disk usage
    network: {
      upload: 0, // TODO: Get actual network stats
      download: 0,
    },
    timestamp: new Date(),
  };

  const response: ApiResponse<{ metrics: SystemMetrics }> = {
    success: true,
    data: {
      metrics,
    },
    message: 'System metrics endpoint - TODO: Implement actual metrics collection',
  };

  res.json(response);
}));

// Health check endpoint
router.get('/health', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  try {
    // Basic health checks
    const [dbHealth, redisHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth(),
    ]);

    const responseTime = Date.now() - startTime;
    const isHealthy = dbHealth.status === 'healthy' && redisHealth.status === 'healthy';

    const response = {
      status: isHealthy ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime,
      checks: {
        database: dbHealth,
        redis: redisHealth,
      },
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    };

    res.status(isHealthy ? 200 : 503).json(response);
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    });
  }
}));

// Admin-only endpoints
router.use(authenticate, authorize('ADMIN'));

// Get detailed system information
router.get('/info', asyncHandler(async (req, res) => {
  const systemInfo = {
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
    },
    memory: process.memoryUsage(),
    environment: {
      nodeEnv: process.env.NODE_ENV,
      port: process.env.PORT,
      // Don't expose sensitive environment variables
    },
    timestamp: new Date().toISOString(),
  };

  const response: ApiResponse<typeof systemInfo> = {
    success: true,
    data: systemInfo,
  };

  res.json(response);
}));

export default router;
