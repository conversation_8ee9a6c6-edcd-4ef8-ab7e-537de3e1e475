'use client';

import { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Send, 
  Bot, 
  User, 
  Plus, 
  MessageSquare,
  Loader2,
  Settings,
  Trash2
} from 'lucide-react';
import { cn, formatRelativeTime } from '@/lib/utils';
import { ollamaApi } from '@/lib/api';
import { useWebSocket } from '@/hooks/useWebSocket';
import { WS_EVENTS } from '@shared/utils/constants';
import { toast } from 'react-hot-toast';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface Conversation {
  id: string;
  title: string;
  model: string;
  messages: Message[];
  updatedAt: Date;
}

export default function ChatPage() {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const { sendMessage: sendWebSocketMessage, addEventListener } = useWebSocket();

  // Fetch available models
  const { data: modelsData } = useQuery({
    queryKey: ['ollama', 'models'],
    queryFn: ollamaApi.listModels,
  });

  // Fetch conversations
  const { data: conversationsData, isLoading: conversationsLoading } = useQuery({
    queryKey: ['ollama', 'conversations'],
    queryFn: () => ollamaApi.getConversations({ limit: 50 }),
  });

  // Fetch specific conversation
  const { data: conversationData } = useQuery({
    queryKey: ['ollama', 'conversation', selectedConversation],
    queryFn: () => selectedConversation ? ollamaApi.getConversation(selectedConversation) : null,
    enabled: !!selectedConversation,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: ollamaApi.sendChatMessage,
    onSuccess: (data) => {
      // Message will be handled via WebSocket
      queryClient.invalidateQueries({ queryKey: ['ollama', 'conversations'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message');
      setIsStreaming(false);
    },
  });

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: ollamaApi.deleteConversation,
    onSuccess: () => {
      toast.success('Conversation deleted');
      queryClient.invalidateQueries({ queryKey: ['ollama', 'conversations'] });
      if (selectedConversation) {
        setSelectedConversation(null);
        setMessages([]);
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete conversation');
    },
  });

  const models = modelsData?.data?.models || [];
  const conversations = conversationsData?.data?.items || [];

  // Set default model
  useEffect(() => {
    if (models.length > 0 && !selectedModel) {
      setSelectedModel(models[0].name);
    }
  }, [models, selectedModel]);

  // Load conversation messages
  useEffect(() => {
    if (conversationData?.data?.conversation) {
      setMessages(conversationData.data.conversation.messages || []);
      setSelectedModel(conversationData.data.conversation.model);
    }
  }, [conversationData]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // WebSocket event listeners
  useEffect(() => {
    const unsubscribeStream = addEventListener(WS_EVENTS.CHAT_STREAM, (data) => {
      if (data.conversationId === selectedConversation) {
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            // Update existing assistant message
            return prev.map((msg, index) => 
              index === prev.length - 1 
                ? { ...msg, content: msg.content + data.content }
                : msg
            );
          } else {
            // Add new assistant message
            return [...prev, {
              id: Date.now().toString(),
              role: 'assistant',
              content: data.content,
              timestamp: new Date(),
            }];
          }
        });

        if (data.done) {
          setIsStreaming(false);
        }
      }
    });

    const unsubscribeError = addEventListener(WS_EVENTS.CHAT_ERROR, (data) => {
      toast.error(data.error || 'Chat error occurred');
      setIsStreaming(false);
    });

    return () => {
      unsubscribeStream();
      unsubscribeError();
    };
  }, [addEventListener, selectedConversation]);

  const handleSendMessage = async () => {
    if (!message.trim() || !selectedModel || isStreaming) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: message.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsStreaming(true);

    try {
      // Send via WebSocket for real-time streaming
      sendWebSocketMessage(WS_EVENTS.CHAT_MESSAGE, {
        message: userMessage.content,
        model: selectedModel,
        conversationId: selectedConversation,
        stream: true,
      });
    } catch (error) {
      toast.error('Failed to send message');
      setIsStreaming(false);
    }
  };

  const handleNewConversation = () => {
    setSelectedConversation(null);
    setMessages([]);
  };

  const handleDeleteConversation = (conversationId: string) => {
    if (confirm('Are you sure you want to delete this conversation?')) {
      deleteConversationMutation.mutate(conversationId);
    }
  };

  return (
    <div className="flex h-full">
      {/* Sidebar - Conversations */}
      <div className="w-80 border-r bg-card">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-semibold">Conversations</h2>
            <Button size="sm" onClick={handleNewConversation}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger>
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {models.map((model) => (
                <SelectItem key={model.name} value={model.name}>
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2 space-y-2">
            {conversationsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="h-8 w-8 mx-auto mb-2" />
                <p>No conversations yet</p>
                <p className="text-sm">Start a new chat to begin</p>
              </div>
            ) : (
              conversations.map((conversation: any) => (
                <div
                  key={conversation.id}
                  className={cn(
                    'p-3 rounded-lg cursor-pointer transition-colors group',
                    selectedConversation === conversation.id
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted'
                  )}
                  onClick={() => setSelectedConversation(conversation.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{conversation.title}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          {conversation.model}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(conversation.updatedAt)}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteConversation(conversation.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b bg-card">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold">
                {selectedConversation ? 'Chat' : 'New Conversation'}
              </h1>
              {selectedModel && (
                <p className="text-sm text-muted-foreground">
                  Using {selectedModel}
                </p>
              )}
            </div>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4 max-w-4xl mx-auto">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
                <p className="text-muted-foreground">
                  Send a message to begin chatting with {selectedModel || 'the AI model'}
                </p>
              </div>
            ) : (
              messages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn(
                    'flex gap-3',
                    msg.role === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  {msg.role === 'assistant' && (
                    <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border bg-primary text-primary-foreground">
                      <Bot className="h-4 w-4" />
                    </div>
                  )}
                  <div
                    className={cn(
                      'rounded-lg px-4 py-2 max-w-[80%]',
                      msg.role === 'user'
                        ? 'bg-primary text-primary-foreground ml-auto'
                        : 'bg-muted'
                    )}
                  >
                    <p className="whitespace-pre-wrap">{msg.content}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {formatRelativeTime(msg.timestamp)}
                    </p>
                  </div>
                  {msg.role === 'user' && (
                    <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border bg-background">
                      <User className="h-4 w-4" />
                    </div>
                  )}
                </div>
              ))
            )}
            {isStreaming && (
              <div className="flex gap-3 justify-start">
                <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="bg-muted rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Thinking...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="p-4 border-t bg-card">
          <div className="flex gap-2 max-w-4xl mx-auto">
            <Input
              placeholder="Type your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
              disabled={isStreaming || !selectedModel}
              className="flex-1"
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!message.trim() || isStreaming || !selectedModel}
            >
              {isStreaming ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
