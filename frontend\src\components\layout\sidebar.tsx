'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  Bot, 
  Image, 
  Code, 
  Activity, 
  Settings, 
  Menu,
  X,
  MessageSquare,
  Workflow,
  FileCode,
  BarChart3
} from 'lucide-react';

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: BarChart3,
    description: 'System overview and metrics',
  },
  {
    name: 'Chat',
    href: '/chat',
    icon: MessageSquare,
    description: 'Ollama AI conversations',
  },
  {
    name: 'Models',
    href: '/models',
    icon: Bot,
    description: 'Manage Ollama models',
  },
  {
    name: 'Workflows',
    href: '/workflows',
    icon: Workflow,
    description: 'ComfyUI workflow builder',
  },
  {
    name: 'Gallery',
    href: '/gallery',
    icon: Image,
    description: 'Generated images and results',
  },
  {
    name: 'Code',
    href: '/code',
    icon: FileCode,
    description: 'Code projects and editor',
  },
  {
    name: 'System',
    href: '/system',
    icon: Activity,
    description: 'System monitoring and health',
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'Application settings',
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  return (
    <div
      className={cn(
        'flex h-full flex-col border-r bg-card transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between border-b px-4">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
              <Code className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold">AI Dev Stack</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8"
        >
          {isCollapsed ? (
            <Menu className="h-4 w-4" />
          ) : (
            <X className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground',
                isCollapsed && 'justify-center px-2'
              )}
              title={isCollapsed ? item.description : undefined}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              {!isCollapsed && (
                <div className="flex flex-col">
                  <span>{item.name}</span>
                  {!isActive && (
                    <span className="text-xs text-muted-foreground">
                      {item.description}
                    </span>
                  )}
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="border-t p-4">
        {!isCollapsed && (
          <div className="text-xs text-muted-foreground">
            <div>Version 1.0.0</div>
            <div>© 2024 AI Dev Stack</div>
          </div>
        )}
      </div>
    </div>
  );
}
