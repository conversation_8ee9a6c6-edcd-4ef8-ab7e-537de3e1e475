import { getPrismaClient } from '../utils/database';

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/ai_dev_stack_test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.JWT_EXPIRES_IN = '15m';
  process.env.JWT_REFRESH_EXPIRES_IN = '7d';
  
  // Initialize test database
  const prisma = getPrismaClient();
  
  try {
    // Run migrations
    await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    // Clean up any existing data
    await prisma.refreshToken.deleteMany();
    await prisma.message.deleteMany();
    await prisma.conversation.deleteMany();
    await prisma.queueItem.deleteMany();
    await prisma.workflow.deleteMany();
    await prisma.file.deleteMany();
    await prisma.project.deleteMany();
    await prisma.systemMetric.deleteMany();
    await prisma.serviceStatus.deleteMany();
    await prisma.user.deleteMany();
    
    console.log('Test database initialized');
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    throw error;
  }
});

afterAll(async () => {
  const prisma = getPrismaClient();
  
  try {
    // Clean up test data
    await prisma.refreshToken.deleteMany();
    await prisma.message.deleteMany();
    await prisma.conversation.deleteMany();
    await prisma.queueItem.deleteMany();
    await prisma.workflow.deleteMany();
    await prisma.file.deleteMany();
    await prisma.project.deleteMany();
    await prisma.systemMetric.deleteMany();
    await prisma.serviceStatus.deleteMany();
    await prisma.user.deleteMany();
    
    await prisma.$disconnect();
    console.log('Test database cleaned up');
  } catch (error) {
    console.error('Failed to clean up test database:', error);
  }
});

// Mock external services
jest.mock('../services/ollamaService');
jest.mock('../services/comfyuiService');
jest.mock('../services/augmentCodeService');

// Mock Redis for tests
jest.mock('../utils/redis', () => ({
  getRedisClient: jest.fn(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    disconnect: jest.fn(),
  })),
}));

// Mock WebSocket for tests
jest.mock('socket.io', () => ({
  Server: jest.fn(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn(() => ({
      emit: jest.fn(),
    })),
  })),
}));

// Increase timeout for database operations
jest.setTimeout(30000);
