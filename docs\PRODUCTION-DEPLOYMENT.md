# AI Development Stack GUI - Production Deployment Guide

This guide provides comprehensive instructions for deploying the AI Development Stack GUI in production environments.

## Quick Start

For a complete automated production deployment:

```bash
# Clone the repository
git clone <repository-url>
cd ai-development-stack-gui

# Run production deployment
./scripts/deploy-production.sh
```

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 18.04+), macOS 10.15+, or Windows with WSL2
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 20GB free space minimum
- **CPU**: 4+ cores recommended
- **Network**: Internet connection for downloading dependencies

### Required Software

1. **Docker** 20.10.0+ and **Docker Compose** 2.0.0+
2. **Node.js** 18.0.0+ (for build tools)
3. **Git** for version control
4. **OpenSSL** for certificate generation
5. **curl** for health checks

### Optional Software

- **PostgreSQL Client** for database management
- **Redis CLI** for cache management
- **Nginx** for reverse proxy (if not using Docker)

## Deployment Modes

### 1. Production Mode (Default)

Full production deployment with security hardening and monitoring:

```bash
./scripts/deploy-production.sh production
```

Features:
- Production-optimized Docker containers
- Secure environment configuration
- Health monitoring and logging
- Automated backups
- SSL/TLS support

### 2. Staging Mode

Staging environment for testing:

```bash
./scripts/deploy-production.sh staging
```

Features:
- Production-like environment
- Reduced resource allocation
- Debug logging enabled
- Test data seeding

### 3. Update Mode

Update existing deployment:

```bash
./scripts/deploy-production.sh update --backup-first
```

Features:
- Automatic backup before update
- Rolling update strategy
- Configuration preservation
- Rollback capability

## Deployment Options

### Basic Options

```bash
# Full production deployment
./scripts/deploy-production.sh

# Force reinstall all dependencies
./scripts/deploy-production.sh --force-reinstall

# Skip health checks (not recommended)
./scripts/deploy-production.sh --skip-health-check

# Create backup before deployment
./scripts/deploy-production.sh --backup-first
```

### Advanced Options

```bash
# Enable monitoring stack (Prometheus/Grafana)
./scripts/deploy-production.sh --enable-monitoring

# Enable SSL/HTTPS
./scripts/deploy-production.sh --enable-ssl

# Staging deployment with monitoring
./scripts/deploy-production.sh staging --enable-monitoring
```

## Configuration

### Environment Variables

The deployment script automatically generates secure environment files:

- **Root**: `.env` - Main configuration
- **Backend**: `backend/.env.production` - API configuration  
- **Frontend**: `frontend/.env.production` - Web app configuration

### Key Configuration Files

```
ai-development-stack-gui/
├── .env                           # Main environment config
├── docker-compose.prod.yml        # Production Docker Compose
├── backend/.env.production        # Backend configuration
├── frontend/.env.production       # Frontend configuration
├── nginx/nginx.prod.conf          # Nginx configuration
└── security/                     # Security configurations
    ├── ssl/                      # SSL certificates
    └── security.conf             # Security settings
```

### External Services

Update these URLs in your `.env` file:

```env
# External AI Services
OLLAMA_BASE_URL=http://your-ollama-server:11434
COMFYUI_BASE_URL=http://your-comfyui-server:8188
AUGMENT_CODE_API_KEY=your_actual_api_key_here
```

## Security Configuration

### SSL/TLS Setup

1. **Self-signed certificates** (development/testing):
   ```bash
   # Automatically generated during deployment
   ./scripts/deploy-production.sh --enable-ssl
   ```

2. **Production certificates**:
   ```bash
   # Place your certificates in security/ssl/
   cp your-cert.crt security/ssl/server.crt
   cp your-key.key security/ssl/server.key
   chmod 600 security/ssl/server.key
   ```

### Security Hardening

The deployment script automatically applies:

- Secure file permissions (600 for secrets, 644 for configs)
- Docker security best practices
- Network isolation
- Resource limits
- Log rotation
- Firewall-ready configuration

### Secrets Management

Generated secrets are stored in:
- `backups/<timestamp>/secrets.txt` - Backup of all generated secrets
- `.env` files with 600 permissions
- Docker secrets (for sensitive data)

**Important**: Store secrets securely and delete backup files after noting them down.

## Monitoring and Logging

### Built-in Monitoring

- **Health Checks**: Automated service health monitoring
- **Logging**: Centralized log collection and rotation
- **Metrics**: System resource monitoring
- **Alerts**: Email notifications for critical issues

### Optional Monitoring Stack

Enable Prometheus and Grafana:

```bash
./scripts/deploy-production.sh --enable-monitoring

# Access monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3003 (admin/admin)
```

### Log Locations

```
monitoring/
├── logs/
│   ├── health-YYYYMMDD.log      # Health check logs
│   ├── docker-YYYYMMDD.log      # Docker container logs
│   └── system-YYYYMMDD.log      # System logs
└── metrics/                     # Performance metrics
```

## Backup and Recovery

### Automated Backups

The deployment includes automated backup scripts:

```bash
# Database backup
./backups/backup-database.sh

# File backup
./backups/backup-files.sh

# Full system backup
./scripts/backup-system.sh
```

### Backup Schedule

Set up automated backups with cron:

```bash
# Add to crontab
0 2 * * * /path/to/ai-development-stack-gui/backups/backup-database.sh
0 3 * * 0 /path/to/ai-development-stack-gui/backups/backup-files.sh
```

### Recovery

```bash
# Restore from backup
./scripts/restore-backup.sh /path/to/backup

# Rollback deployment
./scripts/deploy-production.sh rollback
```

## Health Monitoring

### Manual Health Check

```bash
# Comprehensive health check
./scripts/health-check-production.sh

# Verbose output
./scripts/health-check-production.sh --verbose

# Continuous monitoring
./scripts/health-check-production.sh --continuous
```

### Health Check Results

- **Exit Code 0**: All systems healthy
- **Exit Code 1**: Some issues detected (degraded)
- **Exit Code 2**: Critical failures (unhealthy)

### Automated Health Monitoring

```bash
# Add to crontab for regular checks
*/5 * * * * /path/to/ai-development-stack-gui/scripts/health-check-production.sh
```

## Service Management

### Docker Compose Commands

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Restart services
docker-compose -f docker-compose.prod.yml restart

# Stop all services
docker-compose -f docker-compose.prod.yml down

# Update and restart
docker-compose -f docker-compose.prod.yml up -d --force-recreate
```

### Individual Service Management

```bash
# Restart specific service
docker-compose -f docker-compose.prod.yml restart backend

# View service logs
docker-compose -f docker-compose.prod.yml logs -f frontend

# Scale services (if supported)
docker-compose -f docker-compose.prod.yml up -d --scale backend=2
```

## Performance Optimization

### Resource Allocation

Default resource limits in `docker-compose.prod.yml`:

```yaml
# Backend
resources:
  limits:
    memory: 1G
    cpus: '1.0'
  reservations:
    memory: 512M
    cpus: '0.5'

# Frontend
resources:
  limits:
    memory: 512M
    cpus: '0.5'
  reservations:
    memory: 256M
    cpus: '0.25'
```

### Database Optimization

PostgreSQL is configured with production optimizations:

- Connection pooling
- Memory allocation
- Query optimization
- WAL configuration
- Checkpoint tuning

### Caching

Redis is configured for:

- Session storage
- API response caching
- Real-time data caching
- Background job queuing

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :3000
   
   # Change ports in docker-compose.prod.yml
   ```

2. **Permission Errors**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod +x scripts/*.sh
   ```

3. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose -f docker-compose.prod.yml exec postgres pg_isready
   
   # Reset database
   docker-compose -f docker-compose.prod.yml down postgres
   docker volume rm ai-development-stack-gui_postgres_data_prod
   ```

4. **Memory Issues**
   ```bash
   # Check memory usage
   docker stats
   
   # Adjust resource limits in docker-compose.prod.yml
   ```

### Log Analysis

```bash
# Check all service logs
docker-compose -f docker-compose.prod.yml logs

# Check specific service
docker-compose -f docker-compose.prod.yml logs backend

# Follow logs in real-time
docker-compose -f docker-compose.prod.yml logs -f --tail=100
```

### Emergency Procedures

1. **Complete System Reset**
   ```bash
   # Stop all services
   docker-compose -f docker-compose.prod.yml down -v
   
   # Remove all data (WARNING: Data loss!)
   docker system prune -a
   docker volume prune
   
   # Redeploy
   ./scripts/deploy-production.sh --force-reinstall
   ```

2. **Rollback to Previous Version**
   ```bash
   # Restore from backup
   ./scripts/restore-backup.sh /path/to/backup
   
   # Or rollback deployment
   ./scripts/deploy-production.sh rollback
   ```

## Maintenance

### Regular Maintenance Tasks

1. **Weekly**:
   - Review health check logs
   - Check disk space usage
   - Verify backup integrity
   - Update security patches

2. **Monthly**:
   - Review and rotate logs
   - Update dependencies
   - Performance analysis
   - Security audit

3. **Quarterly**:
   - Full system backup
   - Disaster recovery testing
   - Security penetration testing
   - Performance optimization review

### Update Procedures

```bash
# Update application
git pull origin main
./scripts/deploy-production.sh update --backup-first

# Update dependencies
./scripts/deploy-production.sh --force-reinstall

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## Support and Documentation

- **Setup Guide**: `docs/SETUP.md`
- **User Guide**: `docs/USER-GUIDE.md`
- **Troubleshooting**: `docs/TROUBLESHOOTING.md`
- **API Documentation**: `docs/API.md`
- **Development Guide**: `docs/DEVELOPMENT.md`

For additional support, check the project repository issues or create a new issue with:
- Deployment logs (`deployment.log`)
- Health check results
- System information
- Error messages
