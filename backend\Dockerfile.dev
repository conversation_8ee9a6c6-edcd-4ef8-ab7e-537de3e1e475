# Development Dockerfile for Backend
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies (including dev dependencies)
RUN npm ci

# Generate Prisma client
RUN npx prisma generate

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port and debugger port
EXPOSE 3001 9229

# Set development environment
ENV NODE_ENV=development

# Start with nodemon for hot reload and debugging
CMD ["npm", "run", "dev", "--", "--inspect=0.0.0.0:9229"]
