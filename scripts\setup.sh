#!/bin/bash

# AI Development Stack GUI Setup Script
# This script helps set up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Check if Node.js is installed (for local development)
    if ! command -v node &> /dev/null; then
        log_warning "Node.js is not installed. This is optional for Docker-only setup."
    else
        NODE_VERSION=$(node --version)
        log_success "Node.js version: $NODE_VERSION"
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        log_warning "npm is not installed. This is optional for Docker-only setup."
    else
        NPM_VERSION=$(npm --version)
        log_success "npm version: $NPM_VERSION"
    fi
    
    log_success "System requirements check completed"
}

setup_environment() {
    log_info "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            log_success "Created backend/.env from example"
        else
            log_warning "backend/.env.example not found, creating basic .env"
            cat > backend/.env << EOF
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_dev_stack
REDIS_URL=redis://localhost:6379
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
OLLAMA_BASE_URL=http://localhost:11434
COMFYUI_BASE_URL=http://localhost:8188
AUGMENT_CODE_API_KEY=your_api_key_here
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=info
EOF
        fi
    else
        log_info "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        if [ -f "frontend/.env.example" ]; then
            cp frontend/.env.example frontend/.env.local
            log_success "Created frontend/.env.local from example"
        else
            log_warning "frontend/.env.example not found, creating basic .env.local"
            cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
EOF
        fi
    else
        log_info "frontend/.env.local already exists"
    fi
    
    # Root environment for Docker Compose
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# Database
POSTGRES_PASSWORD=postgres_dev_password

# JWT
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# External Services
OLLAMA_BASE_URL=http://host.docker.internal:11434
COMFYUI_BASE_URL=http://host.docker.internal:8188
AUGMENT_CODE_API_KEY=your_api_key_here

# Environment
NODE_ENV=development
EOF
        log_success "Created root .env file"
    else
        log_info "Root .env already exists"
    fi
}

install_dependencies() {
    log_info "Installing dependencies..."
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        log_info "Installing root dependencies..."
        npm install
    fi
    
    # Install backend dependencies
    if [ -f "backend/package.json" ]; then
        log_info "Installing backend dependencies..."
        cd backend
        npm install
        cd ..
    fi
    
    # Install frontend dependencies
    if [ -f "frontend/package.json" ]; then
        log_info "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    # Install shared dependencies
    if [ -f "shared/package.json" ]; then
        log_info "Installing shared dependencies..."
        cd shared
        npm install
        cd ..
    fi
    
    log_success "Dependencies installed"
}

setup_database() {
    log_info "Setting up database..."
    
    # Start database services
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    log_info "Running database migrations..."
    cd backend
    npx prisma migrate dev --name init
    
    # Generate Prisma client
    log_info "Generating Prisma client..."
    npx prisma generate
    
    # Seed database
    log_info "Seeding database..."
    npm run db:seed
    
    cd ..
    
    log_success "Database setup completed"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build all images
    docker-compose build
    
    log_success "Docker images built"
}

start_services() {
    log_info "Starting all services..."
    
    # Start all services
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 15
    
    # Check service health
    log_info "Checking service health..."
    
    # Check backend
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        log_success "Backend is healthy"
    else
        log_warning "Backend health check failed"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "Frontend is accessible"
    else
        log_warning "Frontend health check failed"
    fi
    
    log_success "Services started"
}

show_status() {
    log_info "Service status:"
    echo
    docker-compose ps
    echo
    log_info "Application URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:3001"
    echo "  Database: localhost:5432"
    echo "  Redis: localhost:6379"
    echo
    log_info "Admin tools (with --profile tools):"
    echo "  pgAdmin: http://localhost:5050 (<EMAIL> / admin)"
    echo "  Redis Commander: http://localhost:8081"
    echo
    log_info "Sample accounts:"
    echo "  Admin: <EMAIL> / admin123!@#"
    echo "  Demo:  <EMAIL> / demo123!@#"
}

cleanup() {
    log_info "Cleaning up..."
    
    # Stop all services
    docker-compose down
    
    # Remove volumes (optional)
    read -p "Do you want to remove all data volumes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v
        log_success "Volumes removed"
    fi
    
    # Remove images (optional)
    read -p "Do you want to remove Docker images? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down --rmi all
        log_success "Images removed"
    fi
    
    log_success "Cleanup completed"
}

# Main script logic
case "${1:-setup}" in
    "setup")
        log_info "Starting AI Development Stack setup..."
        check_requirements
        setup_environment
        install_dependencies
        setup_database
        build_images
        start_services
        show_status
        log_success "Setup completed successfully!"
        ;;
    "start")
        log_info "Starting services..."
        docker-compose up -d
        show_status
        ;;
    "stop")
        log_info "Stopping services..."
        docker-compose down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting services..."
        docker-compose restart
        show_status
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    "cleanup")
        cleanup
        ;;
    "help")
        echo "AI Development Stack Setup Script"
        echo
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  setup     - Full setup (default)"
        echo "  start     - Start services"
        echo "  stop      - Stop services"
        echo "  restart   - Restart services"
        echo "  status    - Show service status"
        echo "  logs      - Show logs (optionally for specific service)"
        echo "  cleanup   - Clean up containers and optionally volumes/images"
        echo "  help      - Show this help"
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
