import { Server as SocketIOServer } from 'socket.io';
import os from 'os';
import { config } from '@/config';
import { getPrismaClient } from '@/utils/database';
import { checkDatabaseHealth, checkRedisHealth } from '@/utils/redis';
import { logger, logError } from '@/utils/logger';
import { broadcastToAll } from '@/websocket';
import { WS_EVENTS } from '@shared/utils/constants';
import { SystemMetrics, ServiceStatus } from '@shared/types';

let monitoringInterval: NodeJS.Timeout | null = null;
let healthCheckInterval: NodeJS.Timeout | null = null;

// Get system metrics
function getSystemMetrics(): SystemMetrics {
  const cpus = os.cpus();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;

  // Calculate CPU usage (simplified)
  let totalIdle = 0;
  let totalTick = 0;
  
  cpus.forEach(cpu => {
    for (const type in cpu.times) {
      totalTick += cpu.times[type as keyof typeof cpu.times];
    }
    totalIdle += cpu.times.idle;
  });

  const idle = totalIdle / cpus.length;
  const total = totalTick / cpus.length;
  const cpuUsage = 100 - ~~(100 * idle / total);

  return {
    cpu: cpuUsage,
    memory: (usedMem / totalMem) * 100,
    disk: 0, // TODO: Implement disk usage calculation
    network: {
      upload: 0, // TODO: Implement network monitoring
      download: 0,
    },
    timestamp: new Date(),
  };
}

// Check external service health
async function checkExternalServices(): Promise<ServiceStatus[]> {
  const services: ServiceStatus[] = [];

  // Check Ollama
  try {
    const ollamaUrl = config.externalServices.ollama.baseUrl;
    const startTime = Date.now();
    
    // TODO: Implement actual Ollama health check
    // For now, just simulate
    const responseTime = Date.now() - startTime;
    
    services.push({
      name: 'Ollama',
      status: 'offline', // TODO: Implement actual check
      url: ollamaUrl,
      lastCheck: new Date(),
      responseTime,
      error: 'Health check not implemented',
    });
  } catch (error) {
    services.push({
      name: 'Ollama',
      status: 'error',
      url: config.externalServices.ollama.baseUrl,
      lastCheck: new Date(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  // Check ComfyUI
  try {
    const comfyuiUrl = config.externalServices.comfyui.baseUrl;
    const startTime = Date.now();
    
    // TODO: Implement actual ComfyUI health check
    // For now, just simulate
    const responseTime = Date.now() - startTime;
    
    services.push({
      name: 'ComfyUI',
      status: 'offline', // TODO: Implement actual check
      url: comfyuiUrl,
      lastCheck: new Date(),
      responseTime,
      error: 'Health check not implemented',
    });
  } catch (error) {
    services.push({
      name: 'ComfyUI',
      status: 'error',
      url: config.externalServices.comfyui.baseUrl,
      lastCheck: new Date(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  return services;
}

// Store metrics in database
async function storeMetrics(metrics: SystemMetrics): Promise<void> {
  try {
    const prisma = getPrismaClient();
    
    await prisma.systemMetric.create({
      data: {
        cpu: metrics.cpu,
        memory: metrics.memory,
        disk: metrics.disk,
        network: metrics.network,
        timestamp: metrics.timestamp,
      },
    });

    // Clean up old metrics (keep only last 24 hours)
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    await prisma.systemMetric.deleteMany({
      where: {
        timestamp: {
          lt: cutoffTime,
        },
      },
    });
  } catch (error) {
    logError('Failed to store system metrics', error);
  }
}

// Update service status in database
async function updateServiceStatus(services: ServiceStatus[]): Promise<void> {
  try {
    const prisma = getPrismaClient();
    
    for (const service of services) {
      await prisma.serviceStatus.upsert({
        where: { name: service.name },
        update: {
          status: service.status,
          lastCheck: service.lastCheck,
          responseTime: service.responseTime,
          error: service.error,
        },
        create: {
          name: service.name,
          status: service.status,
          url: service.url,
          lastCheck: service.lastCheck,
          responseTime: service.responseTime,
          error: service.error,
        },
      });
    }
  } catch (error) {
    logError('Failed to update service status', error);
  }
}

// Metrics collection loop
async function collectMetrics(io: SocketIOServer): Promise<void> {
  try {
    const metrics = getSystemMetrics();
    
    // Store metrics in database
    await storeMetrics(metrics);
    
    // Broadcast metrics to connected clients
    broadcastToAll(WS_EVENTS.METRICS_UPDATE, {
      metrics,
      timestamp: new Date(),
    });
    
    logger.debug('System metrics collected', { metrics });
  } catch (error) {
    logError('Failed to collect system metrics', error);
  }
}

// Health check loop
async function performHealthChecks(io: SocketIOServer): Promise<void> {
  try {
    // Check internal services
    const [dbHealth, redisHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth(),
    ]);

    // Check external services
    const externalServices = await checkExternalServices();

    // Combine all service statuses
    const allServices: ServiceStatus[] = [
      {
        name: 'Database',
        status: dbHealth.status === 'healthy' ? 'online' : 'offline',
        url: 'postgresql://localhost:5432',
        lastCheck: new Date(),
        responseTime: dbHealth.responseTime,
        error: dbHealth.error,
      },
      {
        name: 'Redis',
        status: redisHealth.status === 'healthy' ? 'online' : 'offline',
        url: 'redis://localhost:6379',
        lastCheck: new Date(),
        responseTime: redisHealth.responseTime,
        error: redisHealth.error,
      },
      ...externalServices,
    ];

    // Update service status in database
    await updateServiceStatus(allServices);

    // Broadcast status to connected clients
    broadcastToAll(WS_EVENTS.SYSTEM_STATUS, {
      services: allServices,
      timestamp: new Date(),
    });

    logger.debug('Health checks completed', { 
      services: allServices.map(s => ({ name: s.name, status: s.status })) 
    });
  } catch (error) {
    logError('Failed to perform health checks', error);
  }
}

// Start system monitoring
export function startSystemMonitoring(io: SocketIOServer): void {
  logger.info('Starting system monitoring...');

  // Start metrics collection
  monitoringInterval = setInterval(() => {
    collectMetrics(io);
  }, config.monitoring.metricsInterval);

  // Start health checks
  healthCheckInterval = setInterval(() => {
    performHealthChecks(io);
  }, config.monitoring.healthCheckInterval);

  // Perform initial checks
  collectMetrics(io);
  performHealthChecks(io);

  logger.info('System monitoring started', {
    metricsInterval: config.monitoring.metricsInterval,
    healthCheckInterval: config.monitoring.healthCheckInterval,
  });
}

// Stop system monitoring
export function stopSystemMonitoring(): void {
  if (monitoringInterval) {
    clearInterval(monitoringInterval);
    monitoringInterval = null;
  }

  if (healthCheckInterval) {
    clearInterval(healthCheckInterval);
    healthCheckInterval = null;
  }

  logger.info('System monitoring stopped');
}

// Get current system status
export async function getCurrentSystemStatus(): Promise<{
  metrics: SystemMetrics;
  services: ServiceStatus[];
}> {
  const metrics = getSystemMetrics();
  
  const [dbHealth, redisHealth] = await Promise.all([
    checkDatabaseHealth(),
    checkRedisHealth(),
  ]);

  const externalServices = await checkExternalServices();

  const services: ServiceStatus[] = [
    {
      name: 'Database',
      status: dbHealth.status === 'healthy' ? 'online' : 'offline',
      url: 'postgresql://localhost:5432',
      lastCheck: new Date(),
      responseTime: dbHealth.responseTime,
      error: dbHealth.error,
    },
    {
      name: 'Redis',
      status: redisHealth.status === 'healthy' ? 'online' : 'offline',
      url: 'redis://localhost:6379',
      lastCheck: new Date(),
      responseTime: redisHealth.responseTime,
      error: redisHealth.error,
    },
    ...externalServices,
  ];

  return { metrics, services };
}
