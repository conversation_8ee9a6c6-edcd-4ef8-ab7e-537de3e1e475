'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Search, 
  Download, 
  Trash2, 
  Eye, 
  Filter,
  Grid3X3,
  List,
  Calendar,
  Clock,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { formatDate, formatRelativeTime, formatBytes } from '@/lib/utils';
import { comfyuiApi } from '@/lib/api';
import { useWebSocket } from '@/hooks/useWebSocket';
import { WS_EVENTS } from '@shared/utils/constants';
import { toast } from 'react-hot-toast';

interface GeneratedImage {
  id: string;
  filename: string;
  url: string;
  thumbnail: string;
  workflow: {
    id: string;
    name: string;
  };
  metadata: {
    width: number;
    height: number;
    size: number;
    format: string;
    prompt?: string;
    model?: string;
    steps?: number;
    cfg?: number;
    seed?: number;
  };
  createdAt: string;
  status: 'generating' | 'completed' | 'failed';
}

export default function GalleryPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [images, setImages] = useState<GeneratedImage[]>([]);
  const queryClient = useQueryClient();
  const { addEventListener } = useWebSocket();

  // Fetch generation history
  const { data: historyData, isLoading, refetch } = useQuery({
    queryKey: ['comfyui', 'history'],
    queryFn: () => comfyuiApi.getHistory({ limit: 100 }),
    refetchInterval: 30000,
  });

  // Listen for real-time generation updates
  useEffect(() => {
    const unsubscribeProgress = addEventListener(WS_EVENTS.WORKFLOW_PROGRESS, (data) => {
      // Update image status based on progress
      setImages(prev => prev.map(img => 
        img.workflow.id === data.workflowId 
          ? { ...img, status: data.progress < 100 ? 'generating' : 'completed' }
          : img
      ));
    });

    const unsubscribeComplete = addEventListener(WS_EVENTS.GENERATION_COMPLETE, (data) => {
      // Add new completed images
      if (data.results && data.results.length > 0) {
        const newImages = data.results.map((filename: string, index: number) => ({
          id: `${data.workflowId}_${index}`,
          filename,
          url: `/api/images/${filename}`,
          thumbnail: `/api/images/thumbnails/${filename}`,
          workflow: {
            id: data.workflowId,
            name: 'Generated Workflow',
          },
          metadata: {
            width: 512,
            height: 512,
            size: 1024000,
            format: 'PNG',
          },
          createdAt: new Date().toISOString(),
          status: 'completed' as const,
        }));

        setImages(prev => [...newImages, ...prev]);
        toast.success(`Generated ${newImages.length} new image(s)`);
      }
    });

    return () => {
      unsubscribeProgress();
      unsubscribeComplete();
    };
  }, [addEventListener]);

  // Initialize images from history data
  useEffect(() => {
    if (historyData?.data?.items) {
      const historyImages = historyData.data.items
        .filter((item: any) => item.status === 'COMPLETED' && item.results?.images)
        .flatMap((item: any) => 
          item.results.images.map((image: any, index: number) => ({
            id: `${item.id}_${index}`,
            filename: image.filename,
            url: image.url,
            thumbnail: image.thumbnail,
            workflow: {
              id: item.workflowId,
              name: item.workflow?.name || 'Unknown Workflow',
            },
            metadata: {
              width: image.width || 512,
              height: image.height || 512,
              size: image.size || 0,
              format: image.format || 'PNG',
              prompt: item.metadata?.prompt,
              model: item.metadata?.model,
              steps: item.metadata?.steps,
              cfg: item.metadata?.cfg,
              seed: item.metadata?.seed,
            },
            createdAt: item.completedAt || item.createdAt,
            status: 'completed' as const,
          }))
        );
      
      setImages(historyImages);
    }
  }, [historyData]);

  // Filter and sort images
  const filteredImages = images
    .filter(image => {
      const matchesSearch = image.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           image.metadata.prompt?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || image.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'name':
          return a.filename.localeCompare(b.filename);
        case 'size':
          return b.metadata.size - a.metadata.size;
        default:
          return 0;
      }
    });

  const handleDownloadImage = (image: GeneratedImage) => {
    const link = document.createElement('a');
    link.href = image.url;
    link.download = image.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('Image download started');
  };

  const handleDeleteImage = (imageId: string) => {
    if (confirm('Are you sure you want to delete this image?')) {
      setImages(prev => prev.filter(img => img.id !== imageId));
      toast.success('Image deleted');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gallery</h1>
          <p className="text-muted-foreground">
            View and manage your generated images ({filteredImages.length} images)
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
          </Button>
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search images, workflows, or prompts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Newest First</SelectItem>
            <SelectItem value="oldest">Oldest First</SelectItem>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="size">File Size</SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Images</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="generating">Generating</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Images Grid/List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Loading images...</p>
          </div>
        </div>
      ) : filteredImages.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            {searchTerm || filterStatus !== 'all' 
              ? 'No images found matching your criteria.' 
              : 'No images generated yet. Create a workflow and execute it to generate images.'}
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
          {filteredImages.map((image) => (
            <Card key={image.id} className="group overflow-hidden">
              <CardContent className="p-0">
                <div className="relative aspect-square">
                  <Image
                    src={image.thumbnail || image.url}
                    alt={image.filename}
                    fill
                    className="object-cover transition-transform group-hover:scale-105"
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 20vw"
                  />
                  
                  {/* Status Badge */}
                  <div className="absolute top-2 left-2">
                    <Badge 
                      variant={
                        image.status === 'completed' ? 'default' :
                        image.status === 'generating' ? 'secondary' : 'destructive'
                      }
                      className="text-xs"
                    >
                      {image.status === 'generating' && <Loader2 className="w-3 h-3 mr-1 animate-spin" />}
                      {image.status}
                    </Badge>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex gap-1">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => setSelectedImage(image)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl">
                          <DialogHeader>
                            <DialogTitle>{image.filename}</DialogTitle>
                            <DialogDescription>
                              Generated by {image.workflow.name}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 md:grid-cols-2">
                            <div className="relative aspect-square">
                              <Image
                                src={image.url}
                                alt={image.filename}
                                fill
                                className="object-contain rounded-lg"
                                sizes="50vw"
                              />
                            </div>
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2">Image Details</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-muted-foreground">Dimensions:</span>
                                    <span>{image.metadata.width} × {image.metadata.height}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-muted-foreground">Size:</span>
                                    <span>{formatBytes(image.metadata.size)}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-muted-foreground">Format:</span>
                                    <span>{image.metadata.format}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-muted-foreground">Created:</span>
                                    <span>{formatDate(image.createdAt)}</span>
                                  </div>
                                </div>
                              </div>
                              
                              {image.metadata.prompt && (
                                <div>
                                  <h4 className="font-medium mb-2">Prompt</h4>
                                  <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                                    {image.metadata.prompt}
                                  </p>
                                </div>
                              )}
                              
                              <div className="flex gap-2">
                                <Button onClick={() => handleDownloadImage(image)}>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download
                                </Button>
                                <Button 
                                  variant="outline"
                                  onClick={() => handleDeleteImage(image.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </div>
                
                <div className="p-3">
                  <div className="text-sm font-medium truncate">{image.filename}</div>
                  <div className="text-xs text-muted-foreground">
                    {formatRelativeTime(image.createdAt)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {image.metadata.width} × {image.metadata.height}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredImages.map((image) => (
            <Card key={image.id}>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <Image
                      src={image.thumbnail || image.url}
                      alt={image.filename}
                      fill
                      className="object-cover rounded"
                      sizes="64px"
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium truncate">{image.filename}</h3>
                      <Badge 
                        variant={
                          image.status === 'completed' ? 'default' :
                          image.status === 'generating' ? 'secondary' : 'destructive'
                        }
                        className="text-xs"
                      >
                        {image.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {image.workflow.name} • {image.metadata.width} × {image.metadata.height} • {formatBytes(image.metadata.size)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatDate(image.createdAt)}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadImage(image)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteImage(image.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
