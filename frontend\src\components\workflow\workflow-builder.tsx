'use client';

import { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Play, 
  Save, 
  Plus, 
  Trash2, 
  Settings,
  Download,
  Upload,
  Loader2
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
  };
}

interface WorkflowConnection {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
}

interface WorkflowBuilderProps {
  workflow?: {
    id?: string;
    name: string;
    description?: string;
    nodes: WorkflowNode[];
    connections: WorkflowConnection[];
  };
  onSave?: (workflow: any) => void;
  onExecute?: (workflow: any) => void;
}

const NODE_TYPES = [
  { id: 'LoadImage', name: 'Load Image', category: 'Input' },
  { id: 'SaveImage', name: 'Save Image', category: 'Output' },
  { id: 'CheckpointLoaderSimple', name: 'Checkpoint Loader', category: 'Loaders' },
  { id: 'CLIPTextEncode', name: 'CLIP Text Encode', category: 'Conditioning' },
  { id: 'KSampler', name: 'KSampler', category: 'Sampling' },
  { id: 'VAEDecode', name: 'VAE Decode', category: 'VAE' },
  { id: 'VAEEncode', name: 'VAE Encode', category: 'VAE' },
  { id: 'UpscaleModelLoader', name: 'Upscale Model Loader', category: 'Upscaling' },
  { id: 'ImageUpscaleWithModel', name: 'Upscale Image', category: 'Upscaling' },
];

export function WorkflowBuilder({ workflow, onSave, onExecute }: WorkflowBuilderProps) {
  const [nodes, setNodes] = useState<WorkflowNode[]>(workflow?.nodes || []);
  const [connections, setConnections] = useState<WorkflowConnection[]>(workflow?.connections || []);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [workflowName, setWorkflowName] = useState(workflow?.name || 'New Workflow');
  const [workflowDescription, setWorkflowDescription] = useState(workflow?.description || '');
  const [isNodeDialogOpen, setIsNodeDialogOpen] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [draggedNodeType, setDraggedNodeType] = useState<string | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  const addNode = useCallback((nodeType: string, position: { x: number; y: number }) => {
    const nodeTemplate = NODE_TYPES.find(n => n.id === nodeType);
    if (!nodeTemplate) return;

    const newNode: WorkflowNode = {
      id: `${nodeType}_${Date.now()}`,
      type: nodeType,
      position,
      data: {
        label: nodeTemplate.name,
        inputs: {},
        outputs: {},
      },
    };

    setNodes(prev => [...prev, newNode]);
  }, []);

  const removeNode = useCallback((nodeId: string) => {
    setNodes(prev => prev.filter(n => n.id !== nodeId));
    setConnections(prev => prev.filter(c => c.source !== nodeId && c.target !== nodeId));
    if (selectedNode === nodeId) {
      setSelectedNode(null);
    }
  }, [selectedNode]);

  const updateNodeData = useCallback((nodeId: string, data: Partial<WorkflowNode['data']>) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, data: { ...node.data, ...data } }
        : node
    ));
  }, []);

  const handleCanvasDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedNodeType || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    addNode(draggedNodeType, position);
    setDraggedNodeType(null);
  }, [draggedNodeType, addNode]);

  const handleCanvasDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleSaveWorkflow = useCallback(() => {
    const workflowData = {
      id: workflow?.id,
      name: workflowName,
      description: workflowDescription,
      nodes,
      connections,
    };

    if (onSave) {
      onSave(workflowData);
    } else {
      toast.success('Workflow saved locally');
    }
  }, [workflow?.id, workflowName, workflowDescription, nodes, connections, onSave]);

  const handleExecuteWorkflow = useCallback(() => {
    if (nodes.length === 0) {
      toast.error('Cannot execute empty workflow');
      return;
    }

    const workflowData = {
      id: workflow?.id,
      name: workflowName,
      description: workflowDescription,
      nodes,
      connections,
    };

    if (onExecute) {
      onExecute(workflowData);
    } else {
      toast.info('Workflow execution would start here');
    }
  }, [workflow?.id, workflowName, workflowDescription, nodes, connections, onExecute]);

  const exportWorkflow = useCallback(() => {
    const workflowData = {
      name: workflowName,
      description: workflowDescription,
      nodes,
      connections,
    };

    const blob = new Blob([JSON.stringify(workflowData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${workflowName.replace(/\s+/g, '_')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Workflow exported');
  }, [workflowName, workflowDescription, nodes, connections]);

  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="flex h-full">
      {/* Node Library Sidebar */}
      <div className="w-80 border-r bg-card">
        <div className="p-4 border-b">
          <h3 className="font-semibold mb-4">Node Library</h3>
          <div className="space-y-2">
            {Object.entries(
              NODE_TYPES.reduce((acc, node) => {
                if (!acc[node.category]) acc[node.category] = [];
                acc[node.category].push(node);
                return acc;
              }, {} as Record<string, typeof NODE_TYPES>)
            ).map(([category, categoryNodes]) => (
              <div key={category}>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">{category}</h4>
                <div className="space-y-1">
                  {categoryNodes.map((node) => (
                    <div
                      key={node.id}
                      className="p-2 border rounded cursor-move hover:bg-muted transition-colors"
                      draggable
                      onDragStart={() => setDraggedNodeType(node.id)}
                    >
                      <div className="text-sm font-medium">{node.name}</div>
                      <div className="text-xs text-muted-foreground">{node.id}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="p-4 border-b bg-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h2 className="font-semibold">{workflowName}</h2>
              <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Workflow Settings</DialogTitle>
                    <DialogDescription>
                      Configure workflow properties and metadata.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="workflowName">Workflow Name</Label>
                      <Input
                        id="workflowName"
                        value={workflowName}
                        onChange={(e) => setWorkflowName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workflowDescription">Description</Label>
                      <Input
                        id="workflowDescription"
                        value={workflowDescription}
                        onChange={(e) => setWorkflowDescription(e.target.value)}
                        placeholder="Describe what this workflow does..."
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsSettingsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => setIsSettingsDialogOpen(false)}>
                        Save
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={exportWorkflow}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm" onClick={handleSaveWorkflow}>
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
              <Button size="sm" onClick={handleExecuteWorkflow}>
                <Play className="mr-2 h-4 w-4" />
                Execute
              </Button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 flex">
          <div
            ref={canvasRef}
            className="flex-1 relative bg-muted/20 overflow-auto"
            onDrop={handleCanvasDrop}
            onDragOver={handleCanvasDragOver}
          >
            {/* Grid Background */}
            <div 
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px',
              }}
            />

            {/* Nodes */}
            {nodes.map((node) => (
              <div
                key={node.id}
                className={`absolute p-4 bg-card border rounded-lg shadow-sm cursor-pointer transition-all ${
                  selectedNode === node.id ? 'ring-2 ring-primary' : ''
                }`}
                style={{
                  left: node.position.x,
                  top: node.position.y,
                  minWidth: '200px',
                }}
                onClick={() => setSelectedNode(node.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{node.data.label}</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeNode(node.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground">{node.type}</div>
              </div>
            ))}

            {/* Empty State */}
            {nodes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-muted-foreground mb-2">
                    Drag nodes from the library to start building your workflow
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Or click the + button to add nodes manually
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Properties Panel */}
          {selectedNodeData && (
            <div className="w-80 border-l bg-card p-4">
              <h3 className="font-semibold mb-4">Node Properties</h3>
              <div className="space-y-4">
                <div>
                  <Label>Node Type</Label>
                  <div className="text-sm text-muted-foreground">{selectedNodeData.type}</div>
                </div>
                <div>
                  <Label htmlFor="nodeLabel">Label</Label>
                  <Input
                    id="nodeLabel"
                    value={selectedNodeData.data.label}
                    onChange={(e) => updateNodeData(selectedNodeData.id, { label: e.target.value })}
                  />
                </div>
                <div>
                  <Label>Position</Label>
                  <div className="text-sm text-muted-foreground">
                    X: {selectedNodeData.position.x}, Y: {selectedNodeData.position.y}
                  </div>
                </div>
                {/* TODO: Add specific input/output configuration based on node type */}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
