# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the AI Development Stack GUI.

## Quick Diagnostics

### Health Check Script
Run the automated health check to identify issues:
```bash
./scripts/health-check.sh
```

### Service Status
Check if all services are running:
```bash
docker-compose ps
```

### View Logs
Check service logs for errors:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
docker-compose logs -f redis
```

## Common Issues

### 1. Application Won't Start

#### Symptoms
- Cannot access http://localhost:3000
- Docker containers not running
- Error messages during startup

#### Diagnosis
```bash
# Check Docker daemon
docker info

# Check container status
docker-compose ps

# Check for port conflicts
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows
```

#### Solutions

**Port Conflicts**:
```bash
# Find what's using the port
sudo lsof -i :3000

# Kill the process
sudo kill -9 <PID>

# Or change ports in docker-compose.yml
```

**Docker Issues**:
```bash
# Restart Docker daemon
sudo systemctl restart docker  # Linux
# Or restart Docker Desktop

# Reset containers
docker-compose down
docker-compose up -d
```

**Permission Issues**:
```bash
# Fix script permissions
chmod +x scripts/*.sh

# Fix file ownership (Linux/macOS)
sudo chown -R $USER:$USER .
```

### 2. Database Connection Failed

#### Symptoms
- Backend fails to start
- "Database connection failed" errors
- Prisma client errors

#### Diagnosis
```bash
# Check PostgreSQL container
docker-compose exec postgres pg_isready -U postgres

# Check database logs
docker-compose logs postgres

# Test connection
docker-compose exec postgres psql -U postgres -d ai_dev_stack -c "SELECT 1;"
```

#### Solutions

**Database Not Ready**:
```bash
# Wait for database to start
sleep 10

# Restart database
docker-compose restart postgres
```

**Connection String Issues**:
```bash
# Check environment variables
cat backend/.env | grep DATABASE_URL

# Update connection string
DATABASE_URL=********************************************/ai_dev_stack
```

**Database Corruption**:
```bash
# Reset database
docker-compose down postgres
docker volume rm ai-development-stack-gui_postgres_data
docker-compose up -d postgres

# Run migrations
cd backend
npx prisma migrate dev
npm run db:seed
```

### 3. Redis Connection Issues

#### Symptoms
- Session management fails
- WebSocket connections drop
- Cache-related errors

#### Diagnosis
```bash
# Check Redis container
docker-compose exec redis redis-cli ping

# Check Redis logs
docker-compose logs redis

# Test Redis connection
docker-compose exec redis redis-cli info
```

#### Solutions

**Redis Not Responding**:
```bash
# Restart Redis
docker-compose restart redis

# Clear Redis data
docker-compose exec redis redis-cli FLUSHALL
```

**Connection Configuration**:
```bash
# Check Redis URL
cat backend/.env | grep REDIS_URL

# Update Redis URL
REDIS_URL=redis://redis:6379
```

### 4. External Service Connectivity

#### Symptoms
- Ollama models not loading
- ComfyUI workflows fail
- "Service unavailable" errors

#### Diagnosis
```bash
# Test Ollama
curl http://localhost:11434/api/tags

# Test ComfyUI
curl http://localhost:8188/queue

# Check service URLs in environment
cat backend/.env | grep -E "(OLLAMA|COMFYUI)_BASE_URL"
```

#### Solutions

**Service Not Running**:
```bash
# Start Ollama
ollama serve

# Start ComfyUI
cd /path/to/ComfyUI
python main.py --listen
```

**Network Configuration**:
```bash
# Update service URLs for Docker
OLLAMA_BASE_URL=http://host.docker.internal:11434
COMFYUI_BASE_URL=http://host.docker.internal:8188

# For Linux Docker, use host IP
OLLAMA_BASE_URL=http://**********:11434
```

**Firewall Issues**:
```bash
# Allow ports through firewall
sudo ufw allow 11434  # Ollama
sudo ufw allow 8188   # ComfyUI
```

### 5. Frontend Loading Issues

#### Symptoms
- Blank page or loading spinner
- JavaScript errors in browser console
- Build failures

#### Diagnosis
```bash
# Check frontend logs
docker-compose logs frontend

# Check browser console for errors
# Open browser dev tools (F12)

# Check frontend build
cd frontend
npm run build
```

#### Solutions

**Build Errors**:
```bash
# Clear cache and rebuild
cd frontend
rm -rf .next node_modules
npm install
npm run build
```

**Environment Variables**:
```bash
# Check frontend environment
cat frontend/.env.local

# Ensure API URL is correct
NEXT_PUBLIC_API_URL=http://localhost:3001
```

**Memory Issues**:
```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=4096"

# Or update docker-compose.yml
environment:
  NODE_OPTIONS: "--max-old-space-size=4096"
```

### 6. Authentication Problems

#### Symptoms
- Cannot log in
- "Invalid credentials" errors
- Session expires immediately

#### Diagnosis
```bash
# Check JWT secret
cat backend/.env | grep JWT_SECRET

# Check user in database
docker-compose exec postgres psql -U postgres -d ai_dev_stack -c "SELECT * FROM users;"

# Check backend logs for auth errors
docker-compose logs backend | grep -i auth
```

#### Solutions

**Invalid Credentials**:
```bash
# Reset user password
cd backend
npm run db:seed  # This recreates sample users
```

**JWT Issues**:
```bash
# Generate new JWT secret
JWT_SECRET=$(openssl rand -base64 64)
echo "JWT_SECRET=$JWT_SECRET" >> backend/.env

# Restart backend
docker-compose restart backend
```

**Session Problems**:
```bash
# Clear Redis sessions
docker-compose exec redis redis-cli FLUSHALL

# Clear browser cookies and localStorage
# Use browser dev tools
```

### 7. Performance Issues

#### Symptoms
- Slow response times
- High CPU/memory usage
- Timeouts

#### Diagnosis
```bash
# Check system resources
docker stats

# Check service response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3001/health

# Monitor logs for slow queries
docker-compose logs backend | grep -i slow
```

#### Solutions

**Resource Limits**:
```bash
# Increase Docker memory allocation
# Docker Desktop > Settings > Resources > Memory

# Add resource limits to docker-compose.yml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
```

**Database Optimization**:
```bash
# Add database indexes
cd backend
npx prisma db push

# Analyze slow queries
docker-compose exec postgres psql -U postgres -d ai_dev_stack -c "
  SELECT query, mean_time, calls 
  FROM pg_stat_statements 
  ORDER BY mean_time DESC 
  LIMIT 10;"
```

**Cache Configuration**:
```bash
# Optimize Redis memory
docker-compose exec redis redis-cli config set maxmemory 512mb
docker-compose exec redis redis-cli config set maxmemory-policy allkeys-lru
```

## Advanced Troubleshooting

### Debug Mode

#### Enable Debug Logging
```bash
# Backend debug mode
echo "LOG_LEVEL=debug" >> backend/.env

# Frontend debug mode
echo "NODE_ENV=development" >> frontend/.env.local

# Restart services
docker-compose restart
```

#### Node.js Debugging
```bash
# Start backend with debugger
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Connect debugger to port 9229
# Use VS Code, Chrome DevTools, or other debugger
```

### Network Debugging

#### Check Container Networking
```bash
# List Docker networks
docker network ls

# Inspect network
docker network inspect ai-development-stack-gui_default

# Test connectivity between containers
docker-compose exec backend ping postgres
docker-compose exec backend ping redis
```

#### DNS Resolution
```bash
# Check DNS resolution
docker-compose exec backend nslookup postgres
docker-compose exec backend nslookup redis

# Check /etc/hosts
docker-compose exec backend cat /etc/hosts
```

### Database Debugging

#### Query Analysis
```bash
# Enable query logging
docker-compose exec postgres psql -U postgres -c "
  ALTER SYSTEM SET log_statement = 'all';
  SELECT pg_reload_conf();"

# View query logs
docker-compose logs postgres | grep -i select
```

#### Connection Monitoring
```bash
# Check active connections
docker-compose exec postgres psql -U postgres -c "
  SELECT pid, usename, application_name, client_addr, state 
  FROM pg_stat_activity 
  WHERE state = 'active';"
```

### File System Issues

#### Permission Problems
```bash
# Check file permissions
ls -la backend/logs/
ls -la frontend/.next/

# Fix permissions
sudo chown -R $USER:$USER .
chmod -R 755 scripts/
```

#### Disk Space
```bash
# Check disk usage
df -h

# Clean up Docker
docker system prune -a
docker volume prune
```

## Recovery Procedures

### Complete Reset
```bash
# Stop all services
docker-compose down -v

# Remove all data
docker system prune -a
docker volume prune

# Remove local files
rm -rf backend/node_modules frontend/node_modules
rm -rf backend/.next frontend/.next

# Start fresh
./scripts/setup.sh
```

### Backup and Restore

#### Create Backup
```bash
# Database backup
docker-compose exec postgres pg_dump -U postgres ai_dev_stack > backup.sql

# File backup
tar -czf backup.tar.gz backend/uploads frontend/.next
```

#### Restore from Backup
```bash
# Restore database
docker-compose exec -T postgres psql -U postgres ai_dev_stack < backup.sql

# Restore files
tar -xzf backup.tar.gz
```

## Getting Help

### Log Collection
```bash
# Collect all logs
mkdir -p debug-logs
docker-compose logs > debug-logs/docker-compose.log
docker stats --no-stream > debug-logs/docker-stats.log
docker system df > debug-logs/docker-df.log

# System information
uname -a > debug-logs/system-info.log
docker version > debug-logs/docker-version.log
docker-compose version > debug-logs/compose-version.log
```

### Support Information
When seeking help, provide:
1. **Error messages**: Exact error text
2. **Steps to reproduce**: What you were doing when the error occurred
3. **Environment**: OS, Docker version, hardware specs
4. **Logs**: Relevant log files
5. **Configuration**: Environment variables (without secrets)

### Useful Commands
```bash
# Quick status check
docker-compose ps && docker stats --no-stream

# Resource usage
docker system df
docker images
docker volume ls

# Network information
docker network ls
docker-compose exec backend ip route

# Service endpoints
curl -I http://localhost:3000
curl -I http://localhost:3001/health
```
