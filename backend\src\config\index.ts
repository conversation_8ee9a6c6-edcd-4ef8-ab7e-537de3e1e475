import { z } from 'zod';

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3001),
  
  // Database
  DATABASE_URL: z.string().min(1, 'Database URL is required'),
  
  // Redis
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // JWT
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  
  // External Services
  OLLAMA_BASE_URL: z.string().default('http://localhost:11434'),
  COMFYUI_BASE_URL: z.string().default('http://localhost:8188'),
  AUGMENT_CODE_API_KEY: z.string().optional(),
  
  // File Upload
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().default('50MB'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // CORS
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
});

// Validate environment variables
const env = envSchema.parse(process.env);

// Parse file size
function parseFileSize(size: string): number {
  const units = { B: 1, KB: 1024, MB: 1024 ** 2, GB: 1024 ** 3 };
  const match = size.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) throw new Error(`Invalid file size format: ${size}`);
  
  const [, value, unit] = match;
  return Math.floor(parseFloat(value) * units[unit.toUpperCase() as keyof typeof units]);
}

export const config = {
  env: env.NODE_ENV,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',
  
  server: {
    port: env.PORT,
    maxRequestSize: parseFileSize(env.MAX_FILE_SIZE),
  },
  
  database: {
    url: env.DATABASE_URL,
  },
  
  redis: {
    url: env.REDIS_URL,
  },
  
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  
  externalServices: {
    ollama: {
      baseUrl: env.OLLAMA_BASE_URL,
      timeout: 30000,
    },
    comfyui: {
      baseUrl: env.COMFYUI_BASE_URL,
      timeout: 60000,
    },
    augmentCode: {
      apiKey: env.AUGMENT_CODE_API_KEY,
      timeout: 30000,
    },
  },
  
  upload: {
    directory: env.UPLOAD_DIR,
    maxFileSize: parseFileSize(env.MAX_FILE_SIZE),
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    allowedCodeExtensions: ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.go', '.rs', '.php'],
  },
  
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
  },
  
  cors: {
    origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
  },
  
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    standardHeaders: true,
    legacyHeaders: false,
  },
  
  websocket: {
    pingTimeout: 60000,
    pingInterval: 25000,
  },
  
  monitoring: {
    metricsInterval: 30000, // 30 seconds
    healthCheckInterval: 60000, // 1 minute
  },
} as const;

export type Config = typeof config;
