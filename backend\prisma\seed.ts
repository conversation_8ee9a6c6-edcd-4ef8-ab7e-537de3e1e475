import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123!@#', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
    },
  });

  console.log('✅ Created admin user:', admin.email);

  // Create demo user
  const demoPassword = await bcrypt.hash('demo123!@#', 12);
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'demo',
      password: demoPassword,
      firstName: 'Demo',
      lastName: 'User',
      role: 'USER',
    },
  });

  console.log('✅ Created demo user:', demoUser.email);

  // Create sample conversation
  const conversation = await prisma.conversation.create({
    data: {
      title: 'Welcome to AI Development Stack',
      model: 'llama2:7b',
      userId: demoUser.id,
    },
  });

  // Create sample messages
  await prisma.message.createMany({
    data: [
      {
        role: 'user',
        content: 'Hello! Can you help me understand how to use this AI development stack?',
        conversationId: conversation.id,
      },
      {
        role: 'assistant',
        content: 'Hello! I\'d be happy to help you understand the AI Development Stack. This platform integrates three powerful AI tools:\n\n1. **Ollama** - For running local language models and having AI conversations\n2. **ComfyUI** - For creating visual workflows and generating images\n3. **Augment Code** - For AI-powered code development and analysis\n\nYou can navigate between these tools using the sidebar. Each tool has its own interface designed for specific AI tasks. Would you like me to explain any particular component in more detail?',
        conversationId: conversation.id,
      },
    ],
  });

  console.log('✅ Created sample conversation');

  // Create sample project
  const project = await prisma.project.create({
    data: {
      name: 'Sample React Project',
      description: 'A sample React project for demonstration',
      path: '/projects/sample-react',
      language: 'javascript',
      framework: 'react',
      userId: demoUser.id,
    },
  });

  // Create sample files
  await prisma.file.createMany({
    data: [
      {
        name: 'App.js',
        content: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to AI Development Stack</h1>
        <p>
          This is a sample React application to demonstrate the code editor features.
        </p>
      </header>
    </div>
  );
}

export default App;`,
        language: 'javascript',
        path: 'src/App.js',
        projectId: project.id,
      },
      {
        name: 'package.json',
        content: `{
  "name": "sample-react-project",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}`,
        language: 'json',
        path: 'package.json',
        projectId: project.id,
      },
    ],
  });

  console.log('✅ Created sample project and files');

  // Create sample workflow
  const workflow = await prisma.workflow.create({
    data: {
      name: 'Text to Image Generation',
      description: 'A simple workflow for generating images from text prompts',
      nodes: {
        '1': {
          type: 'CLIPTextEncode',
          inputs: {
            text: 'A beautiful landscape with mountains and a lake',
            clip: ['4', 1],
          },
          outputs: {
            conditioning: ['2', 0],
          },
        },
        '2': {
          type: 'KSampler',
          inputs: {
            model: ['4', 0],
            positive: ['1', 0],
            negative: ['7', 0],
            latent_image: ['5', 0],
            seed: 42,
            steps: 20,
            cfg: 8.0,
            sampler_name: 'euler',
            scheduler: 'normal',
            denoise: 1.0,
          },
          outputs: {
            latent: ['3', 0],
          },
        },
        '3': {
          type: 'VAEDecode',
          inputs: {
            samples: ['2', 0],
            vae: ['4', 2],
          },
          outputs: {
            image: ['8', 0],
          },
        },
        '4': {
          type: 'CheckpointLoaderSimple',
          inputs: {
            ckpt_name: 'v1-5-pruned-emaonly.ckpt',
          },
          outputs: {
            model: ['2', 0],
            clip: ['1', 0],
            vae: ['3', 0],
          },
        },
        '5': {
          type: 'EmptyLatentImage',
          inputs: {
            width: 512,
            height: 512,
            batch_size: 1,
          },
          outputs: {
            latent: ['2', 3],
          },
        },
        '7': {
          type: 'CLIPTextEncode',
          inputs: {
            text: 'blurry, low quality, distorted',
            clip: ['4', 1],
          },
          outputs: {
            conditioning: ['2', 1],
          },
        },
        '8': {
          type: 'SaveImage',
          inputs: {
            images: ['3', 0],
            filename_prefix: 'ComfyUI',
          },
          outputs: {},
        },
      },
      connections: [
        { from: '4', fromOutput: 1, to: '1', toInput: 'clip' },
        { from: '1', fromOutput: 0, to: '2', toInput: 'positive' },
        { from: '4', fromOutput: 0, to: '2', toInput: 'model' },
        { from: '5', fromOutput: 0, to: '2', toInput: 'latent_image' },
        { from: '7', fromOutput: 0, to: '2', toInput: 'negative' },
        { from: '2', fromOutput: 0, to: '3', toInput: 'samples' },
        { from: '4', fromOutput: 2, to: '3', toInput: 'vae' },
        { from: '3', fromOutput: 0, to: '8', toInput: 'images' },
        { from: '4', fromOutput: 1, to: '7', toInput: 'clip' },
      ],
      version: '1.0',
      isPublic: true,
      userId: demoUser.id,
    },
  });

  console.log('✅ Created sample workflow');

  // Create sample service status entries
  await prisma.serviceStatus.createMany({
    data: [
      {
        name: 'Ollama',
        status: 'online',
        url: 'http://localhost:11434',
        responseTime: 45,
      },
      {
        name: 'ComfyUI',
        status: 'online',
        url: 'http://localhost:8188',
        responseTime: 120,
      },
      {
        name: 'Augment Code',
        status: 'online',
        url: 'https://api.augmentcode.com',
        responseTime: 200,
      },
    ],
  });

  console.log('✅ Created sample service status entries');

  // Create sample system metrics
  await prisma.systemMetric.create({
    data: {
      cpu: 25.5,
      memory: 60.2,
      disk: 45.8,
      network: {
        upload: 1024,
        download: 2048,
      },
    },
  });

  console.log('✅ Created sample system metrics');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Sample accounts created:');
  console.log('   Admin: <EMAIL> / admin123!@#');
  console.log('   Demo:  <EMAIL> / demo123!@#');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
