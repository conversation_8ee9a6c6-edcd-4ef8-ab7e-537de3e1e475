'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Lightbulb, 
  Check, 
  X, 
  Copy, 
  ChevronRight,
  Sparkles,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useCodeWebSocket } from '@/hooks/useWebSocket';
import { cn } from '@/lib/utils';

interface CodeSuggestion {
  id: string;
  type: 'completion' | 'refactor' | 'optimization' | 'fix' | 'documentation';
  title: string;
  description: string;
  code: string;
  startLine: number;
  endLine: number;
  confidence: number;
  category?: string;
}

interface CodeSuggestionsProps {
  code: string;
  language: string;
  cursorPosition?: { line: number; column: number };
  onApplySuggestion?: (suggestion: CodeSuggestion) => void;
  className?: string;
}

export function CodeSuggestions({
  code,
  language,
  cursorPosition,
  onApplySuggestion,
  className
}: CodeSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<CodeSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null);
  
  const { analyzeCode, onCodeAnalysisResult, onCodeError } = useCodeWebSocket();

  // Listen for analysis results
  useEffect(() => {
    const unsubscribeResult = onCodeAnalysisResult((data) => {
      setIsLoading(false);
      if (data.analysis?.suggestions) {
        setSuggestions(data.analysis.suggestions);
      }
    });

    const unsubscribeError = onCodeError((data) => {
      setIsLoading(false);
      console.error('Code analysis error:', data.error);
    });

    return () => {
      unsubscribeResult();
      unsubscribeError();
    };
  }, [onCodeAnalysisResult, onCodeError]);

  // Analyze code when it changes
  useEffect(() => {
    if (code.trim() && language) {
      setIsLoading(true);
      analyzeCode({ code, language });
    }
  }, [code, language, analyzeCode]);

  const getSuggestionIcon = (type: CodeSuggestion['type']) => {
    switch (type) {
      case 'completion':
        return <Lightbulb className="h-4 w-4 text-blue-500" />;
      case 'refactor':
        return <Sparkles className="h-4 w-4 text-purple-500" />;
      case 'optimization':
        return <ChevronRight className="h-4 w-4 text-green-500" />;
      case 'fix':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'documentation':
        return <Info className="h-4 w-4 text-gray-500" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getSuggestionColor = (type: CodeSuggestion['type']) => {
    switch (type) {
      case 'completion':
        return 'border-blue-200 bg-blue-50';
      case 'refactor':
        return 'border-purple-200 bg-purple-50';
      case 'optimization':
        return 'border-green-200 bg-green-50';
      case 'fix':
        return 'border-red-200 bg-red-50';
      case 'documentation':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const handleApplySuggestion = (suggestion: CodeSuggestion) => {
    onApplySuggestion?.(suggestion);
    setSelectedSuggestion(suggestion.id);
    
    // Auto-deselect after a short delay
    setTimeout(() => {
      setSelectedSuggestion(null);
    }, 2000);
  };

  const handleCopySuggestion = (suggestion: CodeSuggestion) => {
    navigator.clipboard.writeText(suggestion.code);
    // You could show a toast here
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            AI Suggestions
          </CardTitle>
          <CardDescription>
            Analyzing your code for improvements...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          AI Suggestions
          {suggestions.length > 0 && (
            <Badge variant="secondary">{suggestions.length}</Badge>
          )}
        </CardTitle>
        <CardDescription>
          AI-powered code improvements and suggestions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {suggestions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No suggestions available</p>
            <p className="text-sm">Write some code to get AI-powered suggestions</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className={cn(
                    'p-4 rounded-lg border transition-all',
                    getSuggestionColor(suggestion.type),
                    selectedSuggestion === suggestion.id && 'ring-2 ring-primary'
                  )}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getSuggestionIcon(suggestion.type)}
                      <h4 className="font-medium text-sm">{suggestion.title}</h4>
                      <Badge 
                        variant="outline" 
                        className={cn('text-xs', getConfidenceColor(suggestion.confidence))}
                      >
                        {Math.round(suggestion.confidence * 100)}%
                      </Badge>
                    </div>
                    <Badge variant="outline" className="text-xs capitalize">
                      {suggestion.type}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-3">
                    {suggestion.description}
                  </p>
                  
                  {suggestion.code && (
                    <div className="bg-background rounded border p-3 mb-3">
                      <pre className="text-xs overflow-x-auto">
                        <code>{suggestion.code}</code>
                      </pre>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      Lines {suggestion.startLine}-{suggestion.endLine}
                      {suggestion.category && ` • ${suggestion.category}`}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopySuggestion(suggestion)}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleApplySuggestion(suggestion)}
                        disabled={selectedSuggestion === suggestion.id}
                      >
                        {selectedSuggestion === suggestion.id ? (
                          <Check className="h-3 w-3 mr-1" />
                        ) : (
                          <ChevronRight className="h-3 w-3 mr-1" />
                        )}
                        {selectedSuggestion === suggestion.id ? 'Applied' : 'Apply'}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
