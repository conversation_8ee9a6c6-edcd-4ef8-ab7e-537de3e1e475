'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle,
  X,
  Bell,
  BellOff,
  Filter,
  Clock
} from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';
import { useSystemWebSocket } from '@/hooks/useWebSocket';

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  severity: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  message: string;
  source: string;
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
}

interface SystemAlertsProps {
  className?: string;
}

export function SystemAlerts({ className }: SystemAlertsProps) {
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [filter, setFilter] = useState<'all' | 'unresolved' | 'critical'>('unresolved');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const { onSystemStatus, onMetricsUpdate } = useSystemWebSocket();

  // Mock alerts for demonstration
  useEffect(() => {
    const mockAlerts: SystemAlert[] = [
      {
        id: '1',
        type: 'error',
        severity: 'critical',
        title: 'High CPU Usage',
        message: 'CPU usage has exceeded 90% for more than 5 minutes',
        source: 'System Monitor',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        acknowledged: false,
        resolved: false,
      },
      {
        id: '2',
        type: 'warning',
        severity: 'medium',
        title: 'Ollama Service Slow Response',
        message: 'Ollama service response time is above normal (>2s)',
        source: 'Service Monitor',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        acknowledged: true,
        resolved: false,
      },
      {
        id: '3',
        type: 'info',
        severity: 'low',
        title: 'New Model Downloaded',
        message: 'Successfully downloaded llama2:7b model',
        source: 'Ollama',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        acknowledged: true,
        resolved: true,
      },
    ];
    setAlerts(mockAlerts);
  }, []);

  // Listen for real-time system updates to generate alerts
  useEffect(() => {
    const unsubscribeMetrics = onMetricsUpdate((data) => {
      const metrics = data.metrics;
      
      // Generate CPU alert
      if (metrics.cpu > 90) {
        const existingAlert = alerts.find(a => a.title === 'High CPU Usage' && !a.resolved);
        if (!existingAlert) {
          const newAlert: SystemAlert = {
            id: Date.now().toString(),
            type: 'error',
            severity: 'critical',
            title: 'High CPU Usage',
            message: `CPU usage is at ${metrics.cpu.toFixed(1)}%`,
            source: 'System Monitor',
            timestamp: new Date().toISOString(),
            acknowledged: false,
            resolved: false,
          };
          setAlerts(prev => [newAlert, ...prev]);
        }
      }

      // Generate memory alert
      if (metrics.memory > 85) {
        const existingAlert = alerts.find(a => a.title === 'High Memory Usage' && !a.resolved);
        if (!existingAlert) {
          const newAlert: SystemAlert = {
            id: Date.now().toString() + '_mem',
            type: 'warning',
            severity: 'high',
            title: 'High Memory Usage',
            message: `Memory usage is at ${metrics.memory.toFixed(1)}%`,
            source: 'System Monitor',
            timestamp: new Date().toISOString(),
            acknowledged: false,
            resolved: false,
          };
          setAlerts(prev => [newAlert, ...prev]);
        }
      }
    });

    const unsubscribeStatus = onSystemStatus((data) => {
      const services = data.services || [];
      
      // Generate service alerts
      services.forEach((service: any) => {
        if (service.status === 'offline' || service.status === 'error') {
          const existingAlert = alerts.find(a => 
            a.title === `${service.name} Service Down` && !a.resolved
          );
          
          if (!existingAlert) {
            const newAlert: SystemAlert = {
              id: Date.now().toString() + '_' + service.name,
              type: 'error',
              severity: 'critical',
              title: `${service.name} Service Down`,
              message: `${service.name} service is not responding`,
              source: 'Service Monitor',
              timestamp: new Date().toISOString(),
              acknowledged: false,
              resolved: false,
            };
            setAlerts(prev => [newAlert, ...prev]);
          }
        }
      });
    });

    return () => {
      unsubscribeMetrics();
      unsubscribeStatus();
    };
  }, [alerts, onMetricsUpdate, onSystemStatus]);

  const getAlertIcon = (type: SystemAlert['type']) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: SystemAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    switch (filter) {
      case 'unresolved':
        return !alert.resolved;
      case 'critical':
        return alert.severity === 'critical' && !alert.resolved;
      default:
        return true;
    }
  });

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const resolveAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true, acknowledged: true } : alert
    ));
  };

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const unresolvedCount = alerts.filter(a => !a.resolved).length;
  const criticalCount = alerts.filter(a => a.severity === 'critical' && !a.resolved).length;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              System Alerts
              {unresolvedCount > 0 && (
                <Badge variant="destructive">{unresolvedCount}</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Real-time system alerts and notifications
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setNotificationsEnabled(!notificationsEnabled)}
            >
              {notificationsEnabled ? (
                <Bell className="h-4 w-4" />
              ) : (
                <BellOff className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const nextFilter = filter === 'all' ? 'unresolved' : 
                                 filter === 'unresolved' ? 'critical' : 'all';
                setFilter(nextFilter);
              }}
            >
              <Filter className="h-4 w-4 mr-1" />
              {filter}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Alert Summary */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{criticalCount}</div>
            <div className="text-sm text-muted-foreground">Critical</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {alerts.filter(a => a.severity === 'high' && !a.resolved).length}
            </div>
            <div className="text-sm text-muted-foreground">High</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {alerts.filter(a => (a.severity === 'medium' || a.severity === 'low') && !a.resolved).length}
            </div>
            <div className="text-sm text-muted-foreground">Other</div>
          </div>
        </div>

        {/* Alerts List */}
        {filteredAlerts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p>No alerts</p>
            <p className="text-sm">All systems are running normally</p>
          </div>
        ) : (
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {filteredAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    alert.acknowledged ? 'bg-muted/30' : 'bg-background'
                  } ${alert.resolved ? 'opacity-60' : ''}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getAlertIcon(alert.type)}
                      <h4 className="font-medium text-sm">{alert.title}</h4>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getSeverityColor(alert.severity)}`}
                      >
                        {alert.severity}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      {alert.acknowledged && (
                        <Badge variant="outline" className="text-xs">
                          Ack
                        </Badge>
                      )}
                      {alert.resolved && (
                        <Badge variant="outline" className="text-xs text-green-600">
                          Resolved
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-2">
                    {alert.message}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{formatRelativeTime(alert.timestamp)}</span>
                      <span>•</span>
                      <span>{alert.source}</span>
                    </div>
                    
                    {!alert.resolved && (
                      <div className="flex gap-1">
                        {!alert.acknowledged && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => acknowledgeAlert(alert.id)}
                          >
                            Ack
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => resolveAlert(alert.id)}
                        >
                          Resolve
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => dismissAlert(alert.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
