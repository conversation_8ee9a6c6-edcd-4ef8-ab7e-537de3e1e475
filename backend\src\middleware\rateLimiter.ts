import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { config } from '@/config';
import { logSecurity } from '@/utils/logger';
import { getRedisClient } from '@/utils/redis';
import { ApiResponse } from '@shared/types';

// Custom store using Redis
class RedisStore {
  private client = getRedisClient();
  private prefix = 'rate_limit:';

  async increment(key: string): Promise<{ totalHits: number; timeToExpire?: number }> {
    const redisKey = `${this.prefix}${key}`;
    
    try {
      const current = await this.client.incr(redisKey);
      
      if (current === 1) {
        // First request, set expiration
        await this.client.expire(redisKey, config.rateLimit.windowMs / 1000);
      }
      
      const ttl = await this.client.ttl(redisKey);
      
      return {
        totalHits: current,
        timeToExpire: ttl > 0 ? ttl * 1000 : undefined,
      };
    } catch (error) {
      // Fallback to allowing the request if Red<PERSON> is down
      return { totalHits: 1 };
    }
  }

  async decrement(key: string): Promise<void> {
    const redisKey = `${this.prefix}${key}`;
    
    try {
      await this.client.decr(redisKey);
    } catch (error) {
      // Ignore errors for decrement
    }
  }

  async resetKey(key: string): Promise<void> {
    const redisKey = `${this.prefix}${key}`;
    
    try {
      await this.client.del(redisKey);
    } catch (error) {
      // Ignore errors for reset
    }
  }
}

// Rate limiter configuration
export const rateLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  standardHeaders: config.rateLimit.standardHeaders,
  legacyHeaders: config.rateLimit.legacyHeaders,
  store: new RedisStore(),
  
  // Custom key generator
  keyGenerator: (req: Request): string => {
    // Use user ID if authenticated, otherwise use IP
    const userId = (req as any).user?.id;
    return userId ? `user:${userId}` : `ip:${req.ip}`;
  },
  
  // Custom handler for rate limit exceeded
  handler: (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    const identifier = userId ? `user:${userId}` : `ip:${req.ip}`;
    
    logSecurity('Rate limit exceeded', userId, req.ip, {
      identifier,
      method: req.method,
      path: req.path,
      userAgent: req.get('User-Agent'),
    });

    const response: ApiResponse = {
      success: false,
      error: 'Too many requests, please try again later',
    };

    res.status(429).json(response);
  },
  
  // Skip successful requests in development
  skipSuccessfulRequests: config.isDevelopment,
  
  // Skip failed requests
  skipFailedRequests: false,
});

// Stricter rate limiter for authentication endpoints
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore(),
  
  keyGenerator: (req: Request): string => {
    // Always use IP for auth endpoints
    return `auth:${req.ip}`;
  },
  
  handler: (req: Request, res: Response) => {
    logSecurity('Authentication rate limit exceeded', undefined, req.ip, {
      method: req.method,
      path: req.path,
      userAgent: req.get('User-Agent'),
    });

    const response: ApiResponse = {
      success: false,
      error: 'Too many authentication attempts, please try again later',
    };

    res.status(429).json(response);
  },
});

// Rate limiter for file uploads
export const uploadRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 uploads per minute
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore(),
  
  keyGenerator: (req: Request): string => {
    const userId = (req as any).user?.id;
    return userId ? `upload:user:${userId}` : `upload:ip:${req.ip}`;
  },
  
  handler: (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    
    logSecurity('Upload rate limit exceeded', userId, req.ip, {
      method: req.method,
      path: req.path,
      userAgent: req.get('User-Agent'),
    });

    const response: ApiResponse = {
      success: false,
      error: 'Too many upload attempts, please try again later',
    };

    res.status(429).json(response);
  },
});

// Rate limiter for API calls to external services
export const externalApiRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore(),
  
  keyGenerator: (req: Request): string => {
    const userId = (req as any).user?.id;
    return userId ? `external:user:${userId}` : `external:ip:${req.ip}`;
  },
  
  handler: (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    
    logSecurity('External API rate limit exceeded', userId, req.ip, {
      method: req.method,
      path: req.path,
      userAgent: req.get('User-Agent'),
    });

    const response: ApiResponse = {
      success: false,
      error: 'Too many API requests, please try again later',
    };

    res.status(429).json(response);
  },
});

// Helper function to reset rate limit for a user
export async function resetRateLimit(identifier: string): Promise<void> {
  const store = new RedisStore();
  await store.resetKey(identifier);
}

// Helper function to check current rate limit status
export async function getRateLimitStatus(identifier: string): Promise<{
  totalHits: number;
  timeToExpire?: number;
}> {
  const store = new RedisStore();
  return await store.increment(identifier);
}
