'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Download, 
  Trash2, 
  Share2, 
  Copy, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  Maximize,
  X,
  ChevronLeft,
  ChevronRight,
  Info
} from 'lucide-react';
import { formatDate, formatBytes } from '@/lib/utils';
import { toast } from 'react-hot-toast';

interface ImageMetadata {
  id: string;
  filename: string;
  url: string;
  thumbnail: string;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  format: string;
  workflow?: {
    id: string;
    name: string;
    parameters: Record<string, any>;
  };
  generatedAt: string;
  prompt?: string;
  negativePrompt?: string;
  seed?: number;
  steps?: number;
  cfg?: number;
  sampler?: string;
  model?: string;
}

interface ImageViewerProps {
  image: ImageMetadata | null;
  images: ImageMetadata[];
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (imageId: string) => void;
  onDownload?: (image: ImageMetadata) => void;
}

export function ImageViewer({ 
  image, 
  images, 
  isOpen, 
  onClose, 
  onDelete, 
  onDownload 
}: ImageViewerProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [showMetadata, setShowMetadata] = useState(false);

  // Update current index when image changes
  useEffect(() => {
    if (image && images.length > 0) {
      const index = images.findIndex(img => img.id === image.id);
      setCurrentIndex(index >= 0 ? index : 0);
    }
  }, [image, images]);

  // Reset zoom and rotation when image changes
  useEffect(() => {
    setZoom(1);
    setRotation(0);
  }, [currentIndex]);

  const currentImage = images[currentIndex];

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 5));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.1));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleReset = () => {
    setZoom(1);
    setRotation(0);
  };

  const handleDownload = async () => {
    if (!currentImage) return;

    try {
      const response = await fetch(currentImage.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = currentImage.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      onDownload?.(currentImage);
      toast.success('Image downloaded successfully');
    } catch (error) {
      toast.error('Failed to download image');
    }
  };

  const handleCopyUrl = async () => {
    if (!currentImage) return;

    try {
      await navigator.clipboard.writeText(currentImage.url);
      toast.success('Image URL copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  const handleShare = async () => {
    if (!currentImage) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: currentImage.filename,
          url: currentImage.url,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      handleCopyUrl();
    }
  };

  const handleDelete = () => {
    if (!currentImage) return;

    if (confirm('Are you sure you want to delete this image?')) {
      onDelete?.(currentImage.id);
      toast.success('Image deleted successfully');
      
      // Move to next image or close if last image
      if (images.length > 1) {
        if (currentIndex === images.length - 1) {
          setCurrentIndex(currentIndex - 1);
        }
      } else {
        onClose();
      }
    }
  };

  if (!currentImage) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] p-0">
        <div className="flex h-full">
          {/* Main Image Area */}
          <div className="flex-1 relative bg-black">
            {/* Header */}
            <div className="absolute top-0 left-0 right-0 z-10 bg-black/50 backdrop-blur-sm p-4">
              <div className="flex items-center justify-between text-white">
                <div>
                  <h3 className="font-medium">{currentImage.filename}</h3>
                  <p className="text-sm opacity-75">
                    {currentIndex + 1} of {images.length}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowMetadata(!showMetadata)}
                    className="text-white hover:bg-white/20"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Image */}
            <div className="flex items-center justify-center h-full p-16">
              <div 
                className="relative transition-transform duration-200"
                style={{ 
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                  transformOrigin: 'center'
                }}
              >
                <Image
                  src={currentImage.url}
                  alt={currentImage.filename}
                  width={currentImage.dimensions.width}
                  height={currentImage.dimensions.height}
                  className="max-w-full max-h-full object-contain"
                  unoptimized
                />
              </div>
            </div>

            {/* Navigation */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentIndex === 0}
                  className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleNext}
                  disabled={currentIndex === images.length - 1}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}

            {/* Controls */}
            <div className="absolute bottom-0 left-0 right-0 z-10 bg-black/50 backdrop-blur-sm p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleZoomOut}
                    className="text-white hover:bg-white/20"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-white text-sm min-w-[4rem] text-center">
                    {Math.round(zoom * 100)}%
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleZoomIn}
                    className="text-white hover:bg-white/20"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRotate}
                    className="text-white hover:bg-white/20"
                  >
                    <RotateCw className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReset}
                    className="text-white hover:bg-white/20"
                  >
                    <Maximize className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDownload}
                    className="text-white hover:bg-white/20"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleShare}
                    className="text-white hover:bg-white/20"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyUrl}
                    className="text-white hover:bg-white/20"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDelete}
                      className="text-red-400 hover:bg-red-500/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Metadata Panel */}
          {showMetadata && (
            <div className="w-80 bg-background border-l">
              <div className="p-4 border-b">
                <h4 className="font-medium">Image Details</h4>
              </div>
              <ScrollArea className="h-full p-4">
                <div className="space-y-4">
                  {/* Basic Info */}
                  <div>
                    <h5 className="font-medium mb-2">Basic Information</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Filename:</span>
                        <span>{currentImage.filename}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Size:</span>
                        <span>{formatBytes(currentImage.size)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Dimensions:</span>
                        <span>{currentImage.dimensions.width} × {currentImage.dimensions.height}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Format:</span>
                        <span>{currentImage.format.toUpperCase()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Generated:</span>
                        <span>{formatDate(currentImage.generatedAt)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Generation Parameters */}
                  {(currentImage.prompt || currentImage.seed) && (
                    <div>
                      <h5 className="font-medium mb-2">Generation Parameters</h5>
                      <div className="space-y-2 text-sm">
                        {currentImage.prompt && (
                          <div>
                            <span className="text-muted-foreground">Prompt:</span>
                            <p className="mt-1 p-2 bg-muted rounded text-xs">
                              {currentImage.prompt}
                            </p>
                          </div>
                        )}
                        {currentImage.negativePrompt && (
                          <div>
                            <span className="text-muted-foreground">Negative Prompt:</span>
                            <p className="mt-1 p-2 bg-muted rounded text-xs">
                              {currentImage.negativePrompt}
                            </p>
                          </div>
                        )}
                        {currentImage.seed && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Seed:</span>
                            <span>{currentImage.seed}</span>
                          </div>
                        )}
                        {currentImage.steps && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Steps:</span>
                            <span>{currentImage.steps}</span>
                          </div>
                        )}
                        {currentImage.cfg && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">CFG Scale:</span>
                            <span>{currentImage.cfg}</span>
                          </div>
                        )}
                        {currentImage.sampler && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Sampler:</span>
                            <span>{currentImage.sampler}</span>
                          </div>
                        )}
                        {currentImage.model && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Model:</span>
                            <span>{currentImage.model}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Workflow Info */}
                  {currentImage.workflow && (
                    <div>
                      <h5 className="font-medium mb-2">Workflow</h5>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Name:</span>
                          <span>{currentImage.workflow.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">ID:</span>
                          <span className="font-mono text-xs">{currentImage.workflow.id}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
