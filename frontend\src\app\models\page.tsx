'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Download, 
  Trash2, 
  Info, 
  Search, 
  Plus, 
  Loader2,
  HardDrive,
  Calendar,
  Cpu
} from 'lucide-react';
import { formatBytes, formatDate } from '@/lib/utils';
import { ollamaApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function ModelsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [pullModelName, setPullModelName] = useState('');
  const [isPullDialogOpen, setIsPullDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch models
  const { data: modelsData, isLoading, error } = useQuery({
    queryKey: ['ollama', 'models'],
    queryFn: ollamaApi.listModels,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Pull model mutation
  const pullModelMutation = useMutation({
    mutationFn: ollamaApi.pullModel,
    onSuccess: () => {
      toast.success('Model pull initiated successfully');
      queryClient.invalidateQueries({ queryKey: ['ollama', 'models'] });
      setIsPullDialogOpen(false);
      setPullModelName('');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to pull model');
    },
  });

  // Delete model mutation
  const deleteModelMutation = useMutation({
    mutationFn: ollamaApi.deleteModel,
    onSuccess: () => {
      toast.success('Model deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['ollama', 'models'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete model');
    },
  });

  const models = modelsData?.data?.models || [];
  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePullModel = () => {
    if (!pullModelName.trim()) {
      toast.error('Please enter a model name');
      return;
    }
    pullModelMutation.mutate({ name: pullModelName.trim() });
  };

  const handleDeleteModel = (modelName: string) => {
    if (confirm(`Are you sure you want to delete the model "${modelName}"?`)) {
      deleteModelMutation.mutate(modelName);
    }
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Models</h1>
          <p className="text-muted-foreground">Manage your Ollama language models</p>
        </div>
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load models. Please check if Ollama is running and accessible.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Models</h1>
          <p className="text-muted-foreground">
            Manage your Ollama language models ({models.length} models)
          </p>
        </div>
        
        <Dialog open={isPullDialogOpen} onOpenChange={setIsPullDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Pull Model
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Pull New Model</DialogTitle>
              <DialogDescription>
                Download a model from the Ollama registry. Popular models include llama2, codellama, mistral.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="modelName">Model Name</Label>
                <Input
                  id="modelName"
                  placeholder="e.g., llama2:7b, codellama:13b"
                  value={pullModelName}
                  onChange={(e) => setPullModelName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handlePullModel()}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsPullDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handlePullModel}
                  disabled={pullModelMutation.isPending}
                >
                  {pullModelMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Pull Model
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="relative w-full max-w-sm">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search models..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Models Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Loading models...</p>
          </div>
        </div>
      ) : filteredModels.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {searchTerm ? 'No models found matching your search.' : 'No models available. Pull a model to get started.'}
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredModels.map((model) => (
            <Card key={model.name} className="relative">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{model.name}</CardTitle>
                    <CardDescription>
                      {model.details?.family || 'Language Model'}
                    </CardDescription>
                  </div>
                  <Badge variant="secondary">
                    {model.details?.parameter_size || 'Unknown'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span>{formatBytes(model.size)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDate(model.modified_at)}</span>
                  </div>
                  {model.details?.format && (
                    <div className="flex items-center gap-2">
                      <Cpu className="h-4 w-4 text-muted-foreground" />
                      <span>{model.details.format}</span>
                    </div>
                  )}
                  {model.details?.quantization_level && (
                    <div className="flex items-center gap-2">
                      <Info className="h-4 w-4 text-muted-foreground" />
                      <span>{model.details.quantization_level}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      // TODO: Show model details dialog
                      toast.info('Model details coming soon');
                    }}
                  >
                    <Info className="mr-2 h-4 w-4" />
                    Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteModel(model.name)}
                    disabled={deleteModelMutation.isPending}
                    className="text-destructive hover:text-destructive"
                  >
                    {deleteModelMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
