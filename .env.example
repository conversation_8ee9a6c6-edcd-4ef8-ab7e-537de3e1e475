# Environment Configuration
NODE_ENV=development

# Database
POSTGRES_PASSWORD=your_postgres_password
DATABASE_URL=postgresql://postgres:your_postgres_password@localhost:5432/ai_dev_stack

# Redis
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# External Services
OLLAMA_BASE_URL=http://localhost:11434
COMFYUI_BASE_URL=http://localhost:8188
AUGMENT_CODE_API_KEY=your_augment_code_api_key

# Frontend URLs
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50MB

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
