'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Loader2,
  Eye,
  Download,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';
import { useWorkflowWebSocket } from '@/hooks/useWebSocket';
import { toast } from 'react-hot-toast';

interface GenerationTask {
  id: string;
  workflowId: string;
  workflowName: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startedAt: string;
  completedAt?: string;
  estimatedTime?: number;
  results?: string[];
  error?: string;
  parameters?: Record<string, any>;
}

interface GenerationTrackerProps {
  className?: string;
  onViewResults?: (taskId: string, results: string[]) => void;
}

export function GenerationTracker({ className, onViewResults }: GenerationTrackerProps) {
  const [tasks, setTasks] = useState<GenerationTask[]>([]);
  const [isExpanded, setIsExpanded] = useState(true);
  
  const { 
    executeWorkflow,
    onWorkflowStarted, 
    onWorkflowProgress, 
    onGenerationComplete, 
    onWorkflowError 
  } = useWorkflowWebSocket();

  // Listen for workflow events
  useEffect(() => {
    const unsubscribeStarted = onWorkflowStarted((data) => {
      const newTask: GenerationTask = {
        id: data.queueItemId || data.promptId,
        workflowId: data.workflowId,
        workflowName: data.workflowName || 'Unnamed Workflow',
        status: 'running',
        progress: 0,
        startedAt: new Date().toISOString(),
        estimatedTime: data.estimatedTime,
      };
      
      setTasks(prev => [newTask, ...prev]);
      toast.success('Generation started');
    });

    const unsubscribeProgress = onWorkflowProgress((data) => {
      setTasks(prev => prev.map(task => 
        task.id === data.queueItemId || task.workflowId === data.workflowId
          ? { 
              ...task, 
              progress: data.progress || 0,
              status: data.status === 'processing' ? 'running' : task.status
            }
          : task
      ));
    });

    const unsubscribeComplete = onGenerationComplete((data) => {
      setTasks(prev => prev.map(task => 
        task.id === data.queueItemId || task.workflowId === data.workflowId
          ? { 
              ...task, 
              status: 'completed',
              progress: 100,
              completedAt: new Date().toISOString(),
              results: data.results || []
            }
          : task
      ));
      toast.success('Generation completed');
    });

    const unsubscribeError = onWorkflowError((data) => {
      setTasks(prev => prev.map(task => 
        task.id === data.queueItemId || task.workflowId === data.workflowId
          ? { 
              ...task, 
              status: 'failed',
              error: data.error,
              completedAt: new Date().toISOString()
            }
          : task
      ));
      toast.error('Generation failed');
    });

    return () => {
      unsubscribeStarted();
      unsubscribeProgress();
      unsubscribeComplete();
      unsubscribeError();
    };
  }, [onWorkflowStarted, onWorkflowProgress, onGenerationComplete, onWorkflowError]);

  const getStatusIcon = (status: GenerationTask['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: GenerationTask['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateDuration = (task: GenerationTask) => {
    const start = new Date(task.startedAt);
    const end = task.completedAt ? new Date(task.completedAt) : new Date();
    return Math.round((end.getTime() - start.getTime()) / 1000);
  };

  const calculateETA = (task: GenerationTask) => {
    if (task.status !== 'running' || task.progress === 0) return null;
    
    const elapsed = calculateDuration(task);
    const estimated = (elapsed / task.progress) * 100;
    const remaining = estimated - elapsed;
    
    return remaining > 0 ? Math.round(remaining) : null;
  };

  const handleCancelTask = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'cancelled', completedAt: new Date().toISOString() }
        : task
    ));
    toast.success('Task cancelled');
  };

  const handleRetryTask = (task: GenerationTask) => {
    executeWorkflow({
      workflowId: task.workflowId,
      inputs: task.parameters || {}
    });
  };

  const handleRemoveTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const handleViewResults = (task: GenerationTask) => {
    if (task.results && task.results.length > 0) {
      onViewResults?.(task.id, task.results);
    }
  };

  const clearCompleted = () => {
    setTasks(prev => prev.filter(task => 
      task.status === 'running' || task.status === 'pending'
    ));
  };

  const activeTasks = tasks.filter(task => 
    task.status === 'running' || task.status === 'pending'
  );
  const completedTasks = tasks.filter(task => 
    task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled'
  );

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Generation Queue
              {activeTasks.length > 0 && (
                <Badge variant="default">{activeTasks.length}</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Track image generation progress and results
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {completedTasks.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearCompleted}
              >
                Clear Completed
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent>
          {tasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No generation tasks</p>
              <p className="text-sm">Start a workflow to see progress here</p>
            </div>
          ) : (
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="p-3 border rounded-lg space-y-3"
                  >
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <h4 className="font-medium text-sm">{task.workflowName}</h4>
                        <Badge 
                          variant="outline" 
                          className={getStatusColor(task.status)}
                        >
                          {task.status}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        {task.status === 'completed' && task.results && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewResults(task)}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {/* Handle download */}}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                        
                        {task.status === 'failed' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRetryTask(task)}
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        )}
                        
                        {task.status === 'running' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCancelTask(task.id)}
                          >
                            <Square className="h-3 w-3" />
                          </Button>
                        )}
                        
                        {(task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveTask(task.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Progress */}
                    {task.status === 'running' && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Progress: {task.progress}%</span>
                          {calculateETA(task) && (
                            <span>ETA: {calculateETA(task)}s</span>
                          )}
                        </div>
                        <Progress value={task.progress} className="h-1" />
                      </div>
                    )}

                    {/* Details */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Started {formatRelativeTime(task.startedAt)}</span>
                      
                      <div className="flex items-center gap-4">
                        {task.completedAt && (
                          <span>Duration: {calculateDuration(task)}s</span>
                        )}
                        
                        {task.results && task.results.length > 0 && (
                          <span>{task.results.length} result(s)</span>
                        )}
                      </div>
                    </div>

                    {/* Error */}
                    {task.status === 'failed' && task.error && (
                      <div className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                        {task.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      )}
    </Card>
  );
}
